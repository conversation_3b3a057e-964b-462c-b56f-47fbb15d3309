/**
 * Order Management Types Module
 * Contains type definitions for order management and tracking
 * @module types/orders
 */

import { 
  ItemTypeMaster, 
  KaratMaster, 
  MetalColorMaster, // Corrected import name
  CustomerMaster, 
  OrderCategoryMaster, 
  StyleMaster, 
  ProcessMaster, 
  WorkerMaster 
} from './masters';

/**
 * Order status enumeration
 * Defines possible states of a manufacturing order
 * 
 * @enum {string}
 */
export enum OrderStatus {
  Pending = 'pending',
  InProgress = 'in_progress',
  Completed = 'completed',
  Cancelled = 'cancelled'
}

/**
 * Process status enumeration
 * Defines possible states of a manufacturing process
 * 
 * @enum {string}
 */
export enum ProcessStatus {
  Pending = 'pending',
  InProgress = 'in_progress',
  Completed = 'completed',
  Cancelled = 'cancelled'
}

/**
 * Order status display labels
 * Maps order statuses to human-readable labels
 * 
 * @type {Record<OrderStatus, string>}
 */
export const ORDER_STATUS_LABELS: Record<OrderStatus, string> = {
  [OrderStatus.Pending]: 'Pending',
  [OrderStatus.InProgress]: 'In Progress',
  [OrderStatus.Completed]: 'Completed',
  [OrderStatus.Cancelled]: 'Cancelled'
};

/**
 * Process status display labels
 * Maps process statuses to human-readable labels
 * 
 * @type {Record<ProcessStatus, string>}
 */
export const PROCESS_STATUS_LABELS: Record<ProcessStatus, string> = {
  [ProcessStatus.Pending]: 'Pending',
  [ProcessStatus.InProgress]: 'In Progress',
  [ProcessStatus.Completed]: 'Completed',
  [ProcessStatus.Cancelled]: 'Cancelled'
};

/**
 * Order status color scheme
 * Maps order statuses to UI color codes
 * 
 * @type {Record<OrderStatus, string>}
 */
export const ORDER_STATUS_COLORS: Record<OrderStatus, string> = {
  [OrderStatus.Pending]: 'gray',
  [OrderStatus.InProgress]: 'blue',
  [OrderStatus.Completed]: 'green',
  [OrderStatus.Cancelled]: 'red'
};

/**
 * Process status color scheme
 * Maps process statuses to UI color codes
 * 
 * @type {Record<ProcessStatus, string>}
 */
export const PROCESS_STATUS_COLORS: Record<ProcessStatus, string> = {
  [ProcessStatus.Pending]: 'gray',
  [ProcessStatus.InProgress]: 'blue',
  [ProcessStatus.Completed]: 'green',
  [ProcessStatus.Cancelled]: 'red'
};

/**
 * Manufacturing order interface
 * Defines structure of a jewelry manufacturing order
 * 
 * @interface Order
 * @property {string} order_id - Unique order ID (Format: YYMMXXXTT)
 * @property {string} [party_order_no] - External order reference
 * @property {string} [style_code] - Style reference code
 * @property {string} item_type_id - Type of jewelry item
 * @property {string} karat_id - Gold karat reference
 * @property {string} gold_colour_id - Gold color reference
 * @property {string} [third_party_cust_id] - External customer reference
 * @property {string} order_category_id - Order category reference
 * @property {string} issue_date - Order issue date
 * @property {string} [party_delivery_date] - Customer requested delivery date
 * @property {string} expected_delivery_date - Internal delivery target
 * @property {string} [polki_quality] - Polki quality specification
 * @property {string} [diamond_quality] - Diamond quality specification
 * @property {number} [diamond_wt_expected] - Expected diamond weight
 * @property {number} [gold_wt_expected] - Expected gold weight
 * @property {string} [reference_image_url] - Reference image URL
 * @property {boolean} metal_received - Metal materials received
 * @property {boolean} diamonds_received - Diamond materials received
 * @property {boolean} polki_received - Polki materials received
 * @property {string[]} [tags] - Order tags/keywords
 * @property {string} [remarks] - Order notes/remarks
 * @property {string} order_status - Current order status
 * @property {string} [qr_code] - QR code for tracking
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 * @property {ItemTypeMaster} [itemType] - Item type details
 * @property {KaratMaster} [karat] - Karat details
 * @property {GoldColorMaster} [goldColor] - Gold color details
 * @property {CustomerMaster} [customer] - Customer details
 * @property {OrderCategoryMaster} [orderCategory] - Category details
 * @property {StyleMaster} [style] - Style details
 * @property {OrderProcessTracking[]} [processes] - Process tracking details
 */
export interface Order {
  order_id: string;  // Format: YYMMXXXTT (YY=year, MM=month, XXX=sequence, TT=item type)
  order_reference_no: string; // Added to match database schema
  party_order_no?: string;
  style_code?: string;
  item_type_id: string;
  karat_id: string;
  gold_colour_id: string;
  third_party_cust_id?: string;
  order_category_id: string;
  customer_id: string; // Added to match database schema
  issue_date: string;
  party_delivery_date?: string;
  expected_delivery_date: string;
  polki_quality?: string;
  diamond_quality?: string;
  diamond_wt_expected?: number;
  gold_wt_expected?: number;
  reference_image_url?: string;
  metal_received: boolean;
  diamonds_received: boolean;
  polki_received: boolean;
  tags?: string[];
  remarks?: string;
  order_status: string;
  qr_code?: string;

  // New style code management fields
  complexity_level?: number; // 1-5 complexity scale
  is_repeat_order: boolean;
  reference_order_id?: string; // Reference to original order for repeat orders

  created_at: string;
  updated_at: string;

  // Relations (Adjusted to match query structure in OrderQueries)
  itemType?: Pick<ItemTypeMaster, 'item_type_id' | 'description' | 'suffix'> | null;
  karat?: Pick<KaratMaster, 'karat_id' | 'description'> | null;
  goldColor?: Pick<MetalColorMaster, 'metal_colour_id' | 'description'> | null; // Corrected type name
  // Use Pick to match the structure selected in OrderQueries.getOrders
  customer?: Pick<CustomerMaster, 'customer_id' | 'description'> | null;
  orderCategory?: Pick<OrderCategoryMaster, 'order_category_id' | 'description'> | null;
  style?: Pick<StyleMaster, 'style_id' | 'style_code'> | null;
  reference_order?: Pick<Order, 'order_id' | 'order_reference_no' | 'style_code'> | null;
  processes?: OrderProcessTracking[];
}

/**
 * Order process tracking interface
 * Tracks the execution of processes within an order
 * 
 * @interface OrderProcessTracking
 * @property {string} order_id - Reference to order
 * @property {string} process_id - Reference to process
 * @property {string} [worker_id] - Assigned worker reference
 * @property {string} [start_time] - Process start time
 * @property {string} [end_time] - Process end time
 * @property {string} status - Current process status
 * @property {string} [remarks] - Process notes/remarks
 * @property {number} [priority] - Process priority level
 * @property {number} [complexity] - Process complexity level
 * @property {number} [estimated_duration] - Estimated duration
 * @property {ProcessMaster} [process] - Process details
 * @property {WorkerMaster} [worker] - Worker details
 */
export interface OrderProcessTracking {
  order_id: string;
  process_id: string;
  worker_id?: string;
  start_time?: string;
  end_time?: string;
  status: string;
  remarks?: string;
  priority?: number;
  complexity?: number;
  estimated_duration?: number;
  actual_duration?: number;
  created_at: string;
  updated_at: string;
  
  // Relations
  process?: ProcessMaster;
  worker?: WorkerMaster;
}
