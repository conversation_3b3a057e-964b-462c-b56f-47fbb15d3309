/**
 * @fileoverview Defines queries specific to the worker_mast table.
 */

import { BaseQueries } from './base';
import type { Tables } from '../types';
import { handleError } from '../utils';
import { supabase } from '@/lib/db';

// Define the JSON type which appears to be missing
type Json = string | number | boolean | null | { [key: string]: Json } | Json[];

// Utility function for timestamps
const getCurrentTimestamp = () => new Date().toISOString();

/**
 * Worker skill interface
 * Represents a skill assigned to a worker
 * @interface Skill
 */
interface Skill {
  process_id: string;
  skill_level: number;
  assigned_date?: string;
  notes?: string;
}

// Update existing Tables['worker_mast']['Insert'] type with missing properties
// TEMPORARY SOLUTION: This should be consolidated with the database types
type WorkerInsert = Tables['worker_mast']['Insert'] & {
  department_id?: string;
  date_of_joining?: string;
  address?: string;
  image_url?: string;
  working_hours?: Json;
  skills?: Skill[];
  worker_type?: string;
  available_from?: string;
  available_to?: string;
};

// Update existing Tables['worker_mast']['Update'] type with missing properties
type WorkerUpdate = Tables['worker_mast']['Update'] & {
  department_id?: string;
  date_of_joining?: string;
  address?: string;
  image_url?: string;
  working_hours?: Json;
  skills?: Skill[];
  worker_type?: string;
  available_from?: string;
  available_to?: string;
};

/**
 * Handles database operations for the worker_mast table.
 * Includes validation for JSON fields like 'skills'.
 * @class WorkerQueries
 */
export class WorkerQueries extends BaseQueries<'worker_mast'> {
  /**
   * Constructor
   * Initializes with the worker_mast table and its primary key.
   */
  constructor() {
    super('worker_mast', 'worker_id');
  }

  /**
   * Validates the structure of the skills JSON data.
   * @param {any} skills - The skills data to validate.
   * @returns {boolean} True if valid, otherwise throws an error.
   * @throws {Error} If skills data is invalid.
   */
  private static validateSkills(skills: any): boolean {
    if (skills === null || typeof skills !== 'object') {
      // Allow null, but if it's not null, it must be an object
      if (skills !== null) throw new Error('Skills data must be a JSON object or null.');
      return true; // Null is valid
    }

    for (const processId in skills) {
      if (typeof processId !== 'string') {
        throw new Error('Invalid skill entry: Process ID must be a string.');
      }
      const skillDetail = skills[processId];
      if (typeof skillDetail !== 'object' || skillDetail === null) {
        throw new Error(`Invalid skill entry for ${processId}: Must be an object.`);
      }
      if (typeof skillDetail.level !== 'number' || !Number.isInteger(skillDetail.level) || skillDetail.level < 1) {
        throw new Error(`Invalid skill entry for ${processId}: Level must be a positive integer.`);
      }
      // Add more checks if needed (e.g., assigned_date format)
      if (skillDetail.assigned_date && typeof skillDetail.assigned_date !== 'string') {
           // Basic check, could be improved with Date parsing
           throw new Error(`Invalid skill entry for ${processId}: assigned_date must be a string.`);
      }
    }
    return true;
  }

  /**
   * Creates a new worker record with skills validation.
   * Overrides BaseQueries.create to add specific validation.
   * @param {Omit<Tables['worker_mast']['Insert'], 'email' | 'phone_number'> & { skills: Skill[] }} data - The data for the new worker.
   * @returns {Promise<Tables['worker_mast']['Row']>} The created worker record.
   */
  async createWorker(data: Omit<WorkerInsert, 'email' | 'phone_number'> & { skills: Skill[] }): Promise<Tables['worker_mast']['Row']> {
    try {
      WorkerQueries.validateSkills(data.skills);
      // Add validation for other JSON fields if they exist (e.g., working_hours)
      // TEMPORARY FIX: Create a valid worker object from our extended WorkerInsert type
      // This is necessary because the database schema and the TypeScript types are not in sync
      // Should be resolved by consolidating types as per PROJECT_RULES.md Section 1.1
      const workerData: Tables['worker_mast']['Insert'] = {
        name: data.name,
        is_active: data.is_active ?? true,
        shift_start: data.shift_start || '09:00',
        shift_end: data.shift_end || '17:00',
        // Adding required fields that were missing
        worker_type: data.worker_type || 'REGULAR',
        available_from: data.available_from || data.shift_start || '09:00',
        available_to: data.available_to || data.shift_end || '17:00',
        is_vendor: data.is_vendor ?? false,
        efficiency_factor: data.efficiency_factor ?? 1.0,
        // Cast the skills to a JSON string and handle as any to bypass type check
        // In production, this should be properly typed with a database JSON column
        skills: JSON.stringify(data.skills) as any,
        created_at: getCurrentTimestamp(),
        updated_at: getCurrentTimestamp(),
      };
      
      // Call base create method with the properly typed data
      return this.create(workerData);
    } catch (error) {
      console.error('Worker data validation failed on create:', error); // Log context here
      handleError(error); // Pass only the error object
      // throw error; // handleError already throws, so this is redundant
    }
  }

  /**
   * Updates an existing worker record with skills validation.
   * Overrides BaseQueries.update to add specific validation.
   * @param {string} workerId - The ID of the worker to update.
   * @param {Omit<Tables['worker_mast']['Update'], 'email' | 'phone_number'> & { skills: Skill[] }} data - The update data.
   * @returns {Promise<Tables['worker_mast']['Row']>} The updated worker record.
   */
  async updateWorker(workerId: string, data: Omit<WorkerUpdate, 'email' | 'phone_number'> & { skills: Skill[] }): Promise<Tables['worker_mast']['Row']> {
    try {
      if (data.skills !== undefined) {
        // Only validate if skills are part of the update data
        WorkerQueries.validateSkills(data.skills);
      }
      // Add validation for other JSON fields if they exist and are being updated
      // TEMPORARY FIX: Create a valid worker update object from our extended WorkerUpdate type
      // This is necessary because the database schema and the TypeScript types are not in sync
      // Should be resolved by consolidating types as per PROJECT_RULES.md Section 1.1
      const updateData: Tables['worker_mast']['Update'] = {
        name: data.name,
        is_active: data.is_active,
        shift_start: data.shift_start,
        shift_end: data.shift_end,
        // Adding potentially updated fields to match the worker_mast schema
        worker_type: data.worker_type,
        available_from: data.available_from,
        available_to: data.available_to,
        is_vendor: data.is_vendor,
        efficiency_factor: data.efficiency_factor,
        // Cast the skills to a JSON string and handle as any to bypass type check
        // In production, this should be properly typed with a database JSON column
        ...(data.skills && { skills: JSON.stringify(data.skills) as any }),
        updated_at: getCurrentTimestamp(),
      };
      
      // Call base update method with the properly typed data
      return this.update(workerId, updateData);
    } catch (error) {
      console.error('Worker data validation failed on update:', error); // Log context here
      handleError(error); // Pass only the error object
      // throw error; // handleError already throws, so this is redundant
    }
  }

  /**
   * Retrieves a single worker by their ID.
   *
   * @param workerId - The UUID of the worker to retrieve.
   * @returns A promise that resolves to the worker row or null if not found.
   */
  public async getById(workerId: string): Promise<Tables['worker_mast']['Row'] | null> {
    return super.getById(workerId);
  }

  /**
   * Retrieves all worker IDs from the worker_mast table.
   * @returns {Promise<string[]>} A promise that resolves to an array of all worker IDs.
   * @throws {Error} Throws an error if the database query fails.
   */
  async getAllWorkerIds(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from(this.tableName)
        .select(this.pkColumn);

      if (error) {
        handleError(error); // Corrected: handleError expects one argument
        console.error(`Error fetching all worker IDs from ${this.tableName}: ${error?.message ?? 'Unknown error'}`); 
        throw error;
      }

      // Explicitly type item
      return (data || []).map((item: { [key: string]: any }) => item[this.pkColumn]);
    } catch (error) {
      console.error(`[${this.constructor.name}] Error in getAllWorkerIds:`, error);
      throw error;
    }
  }

  /**
   * Deletes multiple worker records in a transaction.
   * Assumes basic orphan checks (orders, history) have been done externally.
   * Add more internal checks if workers have other critical dependencies.
   *
   * @param {string[]} workerIds - An array of worker IDs (UUIDs) to delete.
   * @returns {Promise<void>} A promise that resolves when deletion is complete.
   * @throws {Error} Throws an error if the database query fails.
   */
  public async bulkDelete(workerIds: string[]): Promise<void> {
    if (!workerIds || workerIds.length === 0) {
      console.log('No worker IDs provided for bulk delete.');
      return;
    }

    try {
      // TEMPORARY SOLUTION: Replace withTransaction with direct DB operations
      // Add any critical internal dependency checks here if needed
      // e.g., check worker_skills or other related tables not covered by the script.

      const { error } = await supabase
        .from(this.tableName)
        .delete()
        .in(this.pkColumn, workerIds);

      if (error) {
        handleError(error);
        console.error(`Error deleting workers: ${workerIds.join(', ')}: ${error?.message ?? 'Unknown error'}`);
        throw error;
      }
      console.log(`Successfully deleted ${workerIds.length} worker records.`);
    } catch (error) {
      console.error(`[${this.constructor.name}] Error in bulkDelete:`, error);
      throw error;
    }
  }

  // Add other specific methods for worker_mast if needed
  // e.g., findActiveWorkers, findWorkersBySkill
}

// Export an instance for easy use
export const workerQueries = new WorkerQueries();

// Export for type usage as well
export type { Skill };

// There's no OrderQueries defined in this file
// Remove or comment it out until proper import is added
// export const orderQueries = new OrderQueries();
