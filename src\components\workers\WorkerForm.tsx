'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormField } from '../common/Form/FormField';
import { dbUtils } from '@/lib/db-utils';
import { useAuth } from '@/hooks/useAuth';

interface WorkerFormData {
  name: string;
  contact: string;
  address: string;
  specialization: string;
  status: 'active' | 'inactive';
}

interface WorkerFormProps {
  workerId?: string;
  initialData?: Partial<WorkerFormData>;
  onSuccess?: () => void;
}

export const WorkerForm: React.FC<WorkerFormProps> = ({
  workerId,
  initialData,
  onSuccess,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<WorkerFormData>({
    defaultValues: initialData,
  });

  const onSubmit = async (data: WorkerFormData) => {
    if (!user) return;
    
    setIsSubmitting(true);
    try {
      if (workerId) {
        // Update existing worker
        await dbUtils.update('worker_mast', workerId, {
          ...data,
          updated_at: new Date().toISOString(),
        });

        // Log activity
        await dbUtils.logActivity(
          user.id,
          'worker_updated',
          'worker_mast',
          workerId,
          initialData,
          data
        );
      } else {
        // Create new worker
        // TEMPORARY SOLUTION: Add missing required fields to match the worker_mast schema
        // Per PROJECT_RULES.md Section 2.3, form fields should match database schema exactly
        const newWorker = await dbUtils.create('worker_mast', {
          // Map form fields to database fields
          name: data.name, // Using 'name' instead of 'worker_name'
          is_active: data.status === 'active',
          is_vendor: false, // Default value
          worker_type: 'REGULAR', // Default value
          shift_start: '09:00:00', // Default value
          shift_end: '17:00:00', // Default value
          available_from: new Date().toISOString(), // Default value
          available_to: '2099-12-31T23:59:59Z', // Default value - far future date
          efficiency_factor: 1.0, // Default value
          skills: [], // Empty skills array as default
          // Standard timestamps
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

        if (newWorker) {
          // Log activity
          await dbUtils.logActivity(
            user.id,
            'worker_created',
            'worker_mast',
            newWorker.worker_id,
            null,
            data
          );
        }
      }

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting worker form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <FormField
        label="Name"
        name="name"
        register={register}
        required
        error={errors.name?.message}
        placeholder="Enter worker name"
      />

      <FormField
        label="Contact"
        name="contact"
        register={register}
        required
        error={errors.contact?.message}
        placeholder="Enter contact number"
      />

      <FormField
        label="Address"
        name="address"
        register={register}
        required
        type="textarea"
        error={errors.address?.message}
        placeholder="Enter address"
      />

      <FormField
        label="Specialization"
        name="specialization"
        register={register}
        required
        error={errors.specialization?.message}
        placeholder="Enter specialization"
      />

      <FormField
        label="Status"
        name="status"
        register={register}
        required
        type="select"
        error={errors.status?.message}
      >
        <option value="">Select Status</option>
        <option value="active">Active</option>
        <option value="inactive">Inactive</option>
      </FormField>

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isSubmitting
            ? workerId
              ? 'Updating...'
              : 'Creating...'
            : workerId
            ? 'Update Worker'
            : 'Create Worker'}
        </button>
      </div>
    </form>
  );
};
