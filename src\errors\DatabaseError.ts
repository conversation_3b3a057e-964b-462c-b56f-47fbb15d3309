/**
 * Custom error classes for database operations
 * @module errors/DatabaseError
 */

/**
 * Base class for database-related errors
 * Extends the built-in Error class to provide additional context for database operations
 * 
 * @class DatabaseError
 * @extends {Error}
 */
export class DatabaseError extends Error {
  constructor(
    message: string,
    options?: ErrorOptions
  ) {
    super(message, options);
    this.name = 'DatabaseError';
  }
}

/**
 * Error thrown when a requested entity is not found in the database
 * 
 * @class NotFoundError
 * @extends {DatabaseError}
 * 
 * @example
 * ```typescript
 * throw new NotFoundError('User', '123');
 * // Error: User with id 123 not found
 * ```
 */
export class NotFoundError extends DatabaseError {
  constructor(entity: string, id: string) {
    super(`${entity} with id ${id} not found`);
    this.name = 'NotFoundError';
  }
}

/**
 * Error thrown when a unique constraint is violated
 * Typically occurs when trying to insert a duplicate value in a unique column
 * 
 * @class UniqueConstraintError
 * @extends {DatabaseError}
 * 
 * @example
 * ```typescript
 * throw new UniqueConstraintError('Email already exists');
 * ```
 */
export class UniqueConstraintError extends DatabaseError {
  constructor(detail: string) {
    super(`Unique constraint violation: ${detail}`);
    this.name = 'UniqueConstraintError';
  }
}

/**
 * Error thrown when data validation fails
 * Used for business logic validation before database operations
 * 
 * @class ValidationError
 * @extends {DatabaseError}
 * 
 * @example
 * ```typescript
 * throw new ValidationError('Price must be positive');
 * ```
 */
export class ValidationError extends DatabaseError {
  constructor(message: string) {
    super(`Validation failed: ${message}`);
    this.name = 'ValidationError';
  }
}

/**
 * Error thrown when a foreign key constraint is violated
 * Occurs when trying to reference a non-existent record
 * 
 * @class ForeignKeyError
 * @extends {DatabaseError}
 * 
 * @example
 * ```typescript
 * throw new ForeignKeyError('Referenced user does not exist');
 * ```
 */
export class ForeignKeyError extends DatabaseError {
  constructor(detail: string) {
    super(`Foreign key constraint violation: ${detail}`);
    this.name = 'ForeignKeyError';
  }
}

/**
 * Error thrown when a database transaction fails
 * Used when multiple operations need to be rolled back
 * 
 * @class TransactionError
 * @extends {DatabaseError}
 * 
 * @example
 * ```typescript
 * throw new TransactionError('Failed to update related records');
 * ```
 */
export class TransactionError extends DatabaseError {
  constructor(message: string) {
    super(`Transaction failed: ${message}`);
    this.name = 'TransactionError';
  }
}
