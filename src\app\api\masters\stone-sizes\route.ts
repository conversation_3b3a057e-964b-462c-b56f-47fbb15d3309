import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data, error } = await supabase
      .from('stone_size_mast')
      .select('*')
      .eq('is_active', true)
      .order('size_name');

    if (error) {
      console.error('Error fetching stone sizes:', error);
      return NextResponse.json({ error: 'Failed to fetch stone sizes' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-sizes API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    const { data, error } = await supabase
      .from('stone_size_mast')
      .insert([{
        size_name: body.size_name,
        description: body.description,
        is_active: true
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating stone size:', error);
      return NextResponse.json({ error: 'Failed to create stone size' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-sizes POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Stone size ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('stone_size_mast')
      .update({
        size_name: body.size_name,
        description: body.description,
        updated_at: new Date().toISOString()
      })
      .eq('stone_size_id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating stone size:', error);
      return NextResponse.json({ error: 'Failed to update stone size' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-sizes PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Stone size ID is required' }, { status: 400 });
    }

    const { error } = await supabase
      .from('stone_size_mast')
      .update({ is_active: false })
      .eq('stone_size_id', id);

    if (error) {
      console.error('Error deleting stone size:', error);
      return NextResponse.json({ error: 'Failed to delete stone size' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in stone-sizes DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
