'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMasterData } from '@/hooks/useMasterData';
import {
  ItemTypeMaster,
  PurityMaster,
  MetalColorMaster,
  OrderCategoryMaster,
  CustomerMaster,
  ThirdPartyCustomerMaster
} from '@/types/masters';
import { Order, OrderStatus } from '@/types/orders';
import { createOrder } from '@/services/orderService';
import { toast } from '@/components/ui/use-toast';
import { StyleCodeSelector } from '@/components/orders/StyleCodeSelector';

// Define OrderFormData directly based on form fields
// Use undefined for optional fields
interface OrderFormData {
  customer_id: string;
  third_party_cust_id?: string; // Optional
  party_order_no?: string;      // Optional (Matches common.ts Order)
  style_code?: string;          // Optional (Matches DB schema)
  item_type_id: string;
  purity_id: string;
  metal_colour_id: string;
  order_category_id: string;
  issue_date: string;
  expected_delivery_date: string;
  // estimated_completion_date removed - not in DB schema
  status: OrderStatus;                // Use the enum
  remarks?: string;                   // Optional (null in common.ts, handle mapping)
  metal_received?: boolean;           // Optional (null in common.ts, handle mapping)
  diamonds_received?: boolean;        // Optional (null in common.ts, handle mapping)
  polki_received?: boolean;           // Optional (null in common.ts, handle mapping)
}

export default function NewOrderPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'form' | 'style-code'>('form');
  const [createdOrder, setCreatedOrder] = useState<Order | null>(null);

  // Fetch master data
  const { data: itemTypes } = useMasterData<ItemTypeMaster>({ tableName: 'item_type_mast' });
  const { data: purities } = useMasterData<PurityMaster>({ tableName: 'purity_mast' });
  const { data: metalColors } = useMasterData<MetalColorMaster>({
    tableName: 'metal_colour_mast',
    idField: 'metal_colour_id'
  });
  const { data: orderCategories } = useMasterData<OrderCategoryMaster>({ tableName: 'order_category_mast' });
  const { data: customers } = useMasterData<CustomerMaster>({ tableName: 'customer_mast' });
  const { data: thirdPartyCustomers } = useMasterData<ThirdPartyCustomerMaster>({ 
    tableName: 'third_party_cust_mast',
    idField: 'party_cust_id'
  });

  const [formData, setFormData] = useState<OrderFormData>({
    customer_id: '',
    third_party_cust_id: undefined, // Initialize optional with undefined
    party_order_no: undefined,      // Initialize optional with undefined
    style_code: undefined,          // Initialize optional with undefined (renamed)
    item_type_id: '',
    purity_id: '',
    metal_colour_id: '',
    order_category_id: '', 
    issue_date: '',
    expected_delivery_date: '',
    // estimated_completion_date removed
    status: OrderStatus.Pending,          // Use enum value
    remarks: undefined,                   // Initialize optional with undefined
    metal_received: false,                // Initialize optional booleans (false is ok)
    diamonds_received: false,
    polki_received: false,
  });

  const handleFieldChange = async (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    let parsedValue: string | number | boolean | null | undefined = value;

    if (type === 'number') {
      parsedValue = value ? parseFloat(value) : null;
    } else if (type === 'checkbox') {
      parsedValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'date') {
      parsedValue = value; // Keep date as string
    } else if (value === '' && ['third_party_cust_id'].includes(name)) {
      // Only handle empty select for truly optional foreign keys - use undefined
      parsedValue = undefined;
    } else {
      // Default: treat as string (will be '' for empty required selects)
      parsedValue = value;
    }

    setFormData(prev => ({
      ...prev,
      [name]: parsedValue,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation: Check for empty strings in required fields
    if (!formData.customer_id || !formData.item_type_id || !formData.purity_id || !formData.metal_colour_id || !formData.order_category_id || !formData.issue_date || !formData.expected_delivery_date) {
      alert('Please fill all required fields.');
      return;
    }

    // Prepare payload matching Omit<Order, 'order_id' | 'created_at' | 'updated_at'> from common.ts
    // Map undefined/false to null where needed by the common.ts Order type
    // Rename status to order_status for the service
    const payloadToSend = {
      customer_id: formData.customer_id,
      third_party_cust_id: formData.third_party_cust_id || null, // undefined -> null
      party_order_no: formData.party_order_no || null, // undefined -> null
      style_code: formData.style_code || null,        // Renamed from style_id
      item_type_id: formData.item_type_id,
      purity_id: formData.purity_id,
      metal_colour_id: formData.metal_colour_id,
      order_category_id: formData.order_category_id,
      issue_date: formData.issue_date,
      expected_delivery_date: formData.expected_delivery_date,
      // estimated_completion_date removed
      order_status: formData.status, // RENAME to order_status
      remarks: formData.remarks || null, // undefined -> null
      metal_received: formData.metal_received === undefined ? null : formData.metal_received, // undefined -> null
      diamonds_received: formData.diamonds_received === undefined ? null : formData.diamonds_received, // undefined -> null
      polki_received: formData.polki_received === undefined ? null : formData.polki_received, // undefined -> null
    };

    setLoading(true);
    
    try {
      // Create the order first
      const newOrder = await createOrder(payloadToSend as any);
      setCreatedOrder(newOrder);

      // Move to style code assignment step
      setStep('style-code');

      toast({ title: 'Success', description: 'Order created successfully. Please assign a style code.' });
    } catch (error) {
      console.error('Failed to create order:', error);
      alert('Failed to create order. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleStyleCodeAssigned = (styleCode: string, isRepeat: boolean) => {
    toast({
      title: 'Style Code Assigned',
      description: `Style code "${styleCode}" has been ${isRepeat ? 'linked' : 'created'} for this order.`
    });
    router.push('/orders'); // Redirect to orders list
  };

  const handleStyleCodeCancel = () => {
    // User cancelled style code assignment, but order is already created
    // We could either delete the order or leave it without style code
    toast({
      title: 'Style Code Skipped',
      description: 'Order created without style code assignment. You can assign it later.'
    });
    router.push('/orders');
  };

  // Render style code selector if we're on that step
  if (step === 'style-code' && createdOrder) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <StyleCodeSelector
          order={createdOrder}
          onStyleCodeAssigned={handleStyleCodeAssigned}
          onCancel={handleStyleCodeCancel}
        />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-semibold mb-6 text-gray-900 dark:text-white">New Order</h1>
      <form onSubmit={handleSubmit} className="space-y-6 bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Customer
            </label>
            <select
              name="customer_id"
              value={formData.customer_id || ''}
              onChange={handleFieldChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Select Customer</option>
              {customers?.map(customer => (
                <option key={customer.customer_id} value={customer.customer_id}>
                  {customer.description}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Third Party Customer (Optional)
            </label>
            <select
              name="third_party_cust_id"
              value={formData.third_party_cust_id || ''}
              onChange={handleFieldChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Third Party Customer</option>
              {thirdPartyCustomers?.map(customer => (
                <option key={customer.party_cust_id} value={customer.party_cust_id}>
                  {customer.description}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Party Order No
            </label>
            <input
              type="text"
              name="party_order_no"
              value={formData.party_order_no || ''}
              onChange={handleFieldChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Item Type
            </label>
            <select
              name="item_type_id"
              value={formData.item_type_id || ''}
              onChange={handleFieldChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Select Item Type</option>
              {itemTypes?.map(type => (
                <option key={type.item_type_id} value={type.item_type_id}>
                  {type.description}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Purity
            </label>
            <select
              name="purity_id"
              value={formData.purity_id || ''}
              onChange={handleFieldChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Purity</option>
              {purities?.map((purity) => (
                <option key={purity.purity_id} value={purity.purity_id}>
                  {purity.description} ({purity.purity_percentage}%)
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Metal Color
            </label>
            <select
              name="metal_colour_id"
              value={formData.metal_colour_id || ''}
              onChange={handleFieldChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Metal Color</option>
              {metalColors?.map((color) => (
                <option key={color.metal_colour_id} value={color.metal_colour_id}>
                  {color.description}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Order Category
            </label>
            <select
              name="order_category_id"
              value={formData.order_category_id || ''}
              onChange={handleFieldChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Select Order Category</option>
              {orderCategories?.map((category) => (
                <option key={category.order_category_id} value={category.order_category_id}>
                  {category.description}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Issue Date
            </label>
            <input
              type="date"
              name="issue_date"
              value={formData.issue_date || ''}
              onChange={handleFieldChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Expected Delivery Date
            </label>
            <input
              type="date"
              name="expected_delivery_date"
              value={formData.expected_delivery_date || ''}
              onChange={handleFieldChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          {/* estimated_completion_date field removed */}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Remarks
            </label>
            <textarea
              name="remarks"
              rows={3}
              value={formData.remarks || ''}
              onChange={handleFieldChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        {/* Material Receipt Status */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Material Receipt Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                name="metal_received"
                checked={formData.metal_received || false}
                onChange={handleFieldChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Metal Received</span>
            </label>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                name="diamonds_received"
                checked={formData.diamonds_received || false}
                onChange={handleFieldChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Diamonds Received</span>
            </label>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                name="polki_received"
                checked={formData.polki_received || false}
                onChange={handleFieldChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Polki Received</span>
            </label>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Order'}
          </button>
        </div>
      </form>
    </div>
  );
}
