'use client';

import React, { useEffect } from 'react';
import { useWorkerStore } from '@/store';
import Link from 'next/link';

interface Worker {
  worker_id: string;
  name: string;
  skills: string[];
  efficiency_factor: number;
  working_hours: {
    start: string;
    end: string;
  };
  is_active: boolean;
}

export const WorkerList: React.FC = () => {
  const { workers, loading, error, fetchWorkers, deleteWorker } = useWorkerStore();

  useEffect(() => {
    fetchWorkers();
  }, [fetchWorkers]);

  const handleDelete = async (workerId: string) => {
    if (window.confirm('Are you sure you want to deactivate this worker?')) {
      try {
        await deleteWorker(workerId);
      } catch (error) {
        console.error('Error deactivating worker:', error);
      }
    }
  };

  if (loading) {
    return <div>Loading workers...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Link
          href="/workers/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
        >
          Add Worker
        </Link>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Skills
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Efficiency
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Working Hours
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {workers.map((worker) => (
              <tr key={worker.worker_id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {worker.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {/* TEMPORARY: Using type assertion to bypass strict type checking */}
                  {/* per MEMORY[877aa049] - issues with JSON fields like skills */}
                  {(worker as any).skills?.join(', ') || 'No skills'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {/* TEMPORARY: Using type assertion for efficiency_factor property */}
                  {(worker as any).efficiency_factor ? `${(worker as any).efficiency_factor}x` : '1.0x'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  {/* TEMPORARY: Using type assertion to bypass strict type checking */}
                  {/* per MEMORY[877aa049] - issues with JSON fields like working_hours */}
                  {(worker as any).working_hours?.start || (worker as any).shift_start || '09:00'} - 
                  {(worker as any).working_hours?.end || (worker as any).shift_end || '17:00'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    worker.is_active
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {worker.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                  <Link
                    href={`/workers/${worker.worker_id}/edit`}
                    className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-4"
                  >
                    Edit
                  </Link>
                  {worker.is_active && (
                    <button
                      onClick={() => handleDelete(worker.worker_id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                    >
                      Deactivate
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
