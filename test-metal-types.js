const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://vcimuvdnftocekbqrbfy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTA1MDg2MywiZXhwIjoyMDUwNjI2ODYzfQ.yf0J4tTvvQwFshu8uui7cdYPToPg0d409rQjaDlpCF0';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testMetalTypes() {
  console.log('Testing metal_type_mast table...');
  
  try {
    // First, let's check if the table exists and what data is there
    const { data, error } = await supabase
      .from('metal_type_mast')
      .select('*');
    
    console.log('Current metal types:', data);
    console.log('Error (if any):', error);
    
    // If no data, let's insert some test data
    if (!data || data.length === 0) {
      console.log('No metal types found. Inserting test data...');
      
      const { data: insertData, error: insertError } = await supabase
        .from('metal_type_mast')
        .insert([
          { name: 'Gold', description: 'Gold metal type', is_active: true },
          { name: 'Silver', description: 'Silver metal type', is_active: true },
          { name: 'Platinum', description: 'Platinum metal type', is_active: true }
        ])
        .select();
      
      console.log('Inserted data:', insertData);
      console.log('Insert error (if any):', insertError);
    }
    
  } catch (err) {
    console.error('Test failed:', err);
  }
}

testMetalTypes();
