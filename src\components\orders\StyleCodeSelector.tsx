'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, Plus, Copy } from 'lucide-react';
import { 
  searchExistingStyleCodes, 
  assignNewStyleCode, 
  linkOrderToExistingStyle,
  StyleCodeSearchCriteria,
  StyleCodeMatch 
} from '@/services/styleCodeService';
import { Order } from '@/types/orders';

interface StyleCodeSelectorProps {
  order: Order;
  onStyleCodeAssigned: (styleCode: string, isRepeat: boolean) => void;
  onCancel: () => void;
}

export function StyleCodeSelector({ order, onStyleCodeAssigned, onCancel }: StyleCodeSelectorProps) {
  const [isSearching, setIsSearching] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);
  const [matches, setMatches] = useState<StyleCodeMatch[]>([]);
  const [selectedMatch, setSelectedMatch] = useState<StyleCodeMatch | null>(null);
  const [complexityLevel, setComplexityLevel] = useState<number>(3);
  const [searchPerformed, setSearchPerformed] = useState(false);

  // Auto-search when component mounts
  useEffect(() => {
    handleSearchExisting();
  }, []);

  const handleSearchExisting = async () => {
    setIsSearching(true);
    try {
      const criteria: StyleCodeSearchCriteria = {
        item_type_id: order.item_type_id,
        customer_id: order.customer_id,
        karat_id: order.karat_id,
        gold_colour_id: order.gold_colour_id,
        diamond_quality: order.diamond_quality || undefined,
        polki_quality: order.polki_quality || undefined,
      };

      const results = await searchExistingStyleCodes(criteria);
      setMatches(results);
      setSearchPerformed(true);
    } catch (error) {
      console.error('Error searching existing styles:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectExisting = async (match: StyleCodeMatch) => {
    setIsAssigning(true);
    try {
      await linkOrderToExistingStyle(order.order_id, match.style_code, match.order_id);
      onStyleCodeAssigned(match.style_code, true);
    } catch (error) {
      console.error('Error linking to existing style:', error);
    } finally {
      setIsAssigning(false);
    }
  };

  const handleCreateNew = async () => {
    setIsAssigning(true);
    try {
      const styleCode = await assignNewStyleCode(
        order.order_id, 
        order.order_reference_no, 
        complexityLevel
      );
      onStyleCodeAssigned(styleCode, false);
    } catch (error) {
      console.error('Error creating new style:', error);
    } finally {
      setIsAssigning(false);
    }
  };

  const getComplexityLabel = (level: number): string => {
    const labels = {
      1: 'Easy (3h/10g)',
      2: 'Medium-Easy (4.5h/10g)',
      3: 'Medium (7.5h/10g)',
      4: 'Hard (12h/10g)',
      5: 'Very Hard (15h/10g)'
    };
    return labels[level as keyof typeof labels] || 'Medium';
  };

  const getSimilarityBadgeColor = (score: number): string => {
    if (score >= 95) return 'bg-green-500';
    if (score >= 90) return 'bg-blue-500';
    if (score >= 85) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Style Code Assignment</h2>
        <p className="text-muted-foreground">
          Order: {order.order_reference_no} | Customer: {order.customer?.description}
        </p>
      </div>

      {/* Search Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Existing Style Matches
            {isSearching && <Loader2 className="h-4 w-4 animate-spin" />}
          </CardTitle>
          <CardDescription>
            Found {matches.length} similar styles for this customer and item type
          </CardDescription>
        </CardHeader>
        <CardContent>
          {searchPerformed && matches.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No similar styles found. This appears to be a new design.</p>
            </div>
          )}

          <div className="space-y-3">
            {matches.map((match) => (
              <div
                key={match.style_id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedMatch?.style_id === match.style_id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => setSelectedMatch(match)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge className={getSimilarityBadgeColor(match.similarity_score)}>
                      {match.similarity_score}% match
                    </Badge>
                    <div>
                      <p className="font-medium">Style: {match.style_code}</p>
                      <p className="text-sm text-muted-foreground">
                        From Order: {match.order_reference_no}
                      </p>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectExisting(match);
                    }}
                    disabled={isAssigning}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Use This Style
                  </Button>
                </div>
                
                {match.differences.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-muted-foreground">Differences:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {match.differences.map((diff, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {diff}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Create New Style */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New Style
          </CardTitle>
          <CardDescription>
            Create a new style code for this order (Style Code = Order Number)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Style Code (will be same as order number)
            </label>
            <Input 
              value={order.order_reference_no} 
              disabled 
              className="bg-muted"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Complexity Level (Optional)
            </label>
            <select
              value={complexityLevel}
              onChange={(e) => setComplexityLevel(Number(e.target.value))}
              className="w-full p-2 border border-border rounded-md"
            >
              {[1, 2, 3, 4, 5].map(level => (
                <option key={level} value={level}>
                  Level {level} - {getComplexityLabel(level)}
                </option>
              ))}
            </select>
          </div>

          <Button 
            onClick={handleCreateNew} 
            disabled={isAssigning}
            className="w-full"
          >
            {isAssigning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating Style...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create New Style Code
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
}
