import { createClient } from '@supabase/supabase-js'
import { env } from '@/config/env'
import { Database } from '@/types/database'

export const createServerClient = () => {
  return createClient<Database>(
    env.supabase.url,
    env.supabase.serviceRole,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

export const createBrowserClient = () => {
  return createClient<Database>(
    env.supabase.url,
    env.supabase.anonKey,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true
      }
    }
  )
}
