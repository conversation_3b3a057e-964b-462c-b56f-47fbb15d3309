import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data, error } = await supabase
      .from('metal_type_mast')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching metal types:', error);
      return NextResponse.json({ error: 'Failed to fetch metal types' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    const { data, error } = await supabase
      .from('metal_type_mast')
      .insert([{
        name: body.name,
        description: body.description,
        symbol: body.symbol,
        is_active: true
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating metal type:', error);
      return NextResponse.json({ error: 'Failed to create metal type' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Metal type ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('metal_type_mast')
      .update({
        name: body.name,
        description: body.description,
        symbol: body.symbol,
        updated_at: new Date().toISOString()
      })
      .eq('metal_type_id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating metal type:', error);
      return NextResponse.json({ error: 'Failed to update metal type' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in metal-types PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Metal type ID is required' }, { status: 400 });
    }

    const { error } = await supabase
      .from('metal_type_mast')
      .update({ is_active: false })
      .eq('metal_type_id', id);

    if (error) {
      console.error('Error deleting metal type:', error);
      return NextResponse.json({ error: 'Failed to delete metal type' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in metal-types DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
