{"name": "jwlprocmanage-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "gen:types": "dotenv -e .env.local -- supabase gen types typescript --project-id vcimuvdnftocekbqrbfy --schema public > src/types/db.ts", "migrate": "ts-node scripts/apply-migration.ts", "db:migrate": "npm run migrate && npm run gen:types", "db:fix-rls": "echo 'Please run scripts/simple-rls-fix.sql in Supabase Dashboard SQL Editor'", "db:setup": "echo 'Run both migration and RLS fix, then generate types'", "test:db": "ts-node scripts/test-supabase-connection.ts", "test:auth": "ts-node scripts/test-auth-and-data.ts"}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.6", "@supabase/auth-helpers-nextjs": "^0.7.4", "@supabase/ssr": "^0.0.10", "@supabase/supabase-js": "^2.50.2", "@tanstack/react-query": "^4.36.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dotenv": "^16.6.1", "lucide-react": "^0.279.0", "next": "14.0.4", "next-themes": "^0.2.1", "node-fetch": "^2.7.0", "pg": "^8.14.1", "react": "18.2.0", "react-day-picker": "^8.8.2", "react-dom": "18.2.0", "react-hook-form": "^7.46.1", "react-hot-toast": "^2.4.1", "recharts": "^2.15.2", "sonner": "^2.0.5", "tailwind-merge": "^1.14.0", "zod": "^3.25.67", "zustand": "^4.4.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.6", "@types/node": "^20.17.30", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.15", "dotenv-cli": "^7.3.0", "eslint": "^8.49.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.29", "supabase": "^2.24.3", "tailwindcss": "^3.3.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}