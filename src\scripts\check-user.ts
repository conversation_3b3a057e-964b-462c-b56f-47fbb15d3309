/**
 * <PERSON><PERSON><PERSON> to check if test user exists and create if needed
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://vcimuvdnftocekbqrbfy.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTA1MDg2MywiZXhwIjoyMDUwNjI2ODYzfQ.yf0J4tTvvQwFshu8uui7cdYPToPg0d409rQjaDlpCF0';

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkAndCreateUser() {
  try {
    console.log('Checking for existing users...');
    
    // Check if user exists in auth.users
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    console.log(`Found ${users.users.length} users in the system`);
    
    // Check if our test user exists
    const testUser = users.users.find(user => user.email === '<EMAIL>');
    
    if (testUser) {
      console.log('✅ Test user exists:', testUser.email);
      console.log('User ID:', testUser.id);
      console.log('Created at:', testUser.created_at);
      
      // Check user roles
      const { data: roles, error: rolesError } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', testUser.id);
        
      if (rolesError) {
        console.error('Error fetching user roles:', rolesError);
      } else {
        console.log('User roles:', roles);
      }
      
    } else {
      console.log('❌ Test user does not exist. Creating...');
      
      // Create the test user
      const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
        email: '<EMAIL>',
        password: 'test123',
        email_confirm: true
      });
      
      if (createError) {
        console.error('Error creating user:', createError);
        return;
      }
      
      console.log('✅ User created successfully:', newUser.user?.email);
      
      // Create user role
      if (newUser.user) {
        const { error: roleError } = await supabase
          .from('user_roles')
          .insert({
            user_id: newUser.user.id,
            role: 'admin'
          });
          
        if (roleError) {
          console.error('Error creating user role:', roleError);
        } else {
          console.log('✅ Admin role assigned successfully');
        }
      }
    }
    
  } catch (error) {
    console.error('Script error:', error);
  }
}

// Run the script
checkAndCreateUser();
