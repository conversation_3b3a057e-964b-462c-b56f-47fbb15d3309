/**
 * @fileoverview Defines queries specific to the styles_mast table.
 */

import { BaseQueries } from './base';
import type { Tables } from '../types';
import { handleError } from '../utils';
import { supabase } from '@/lib/db';

/**
 * Handles database operations for the styles_mast table.
 * @class StyleQueries
 */
export class StyleQueries extends BaseQueries<'styles_mast'> {
  /**
   * Constructor
   * Initializes with the styles_mast table and its primary key.
   */
  constructor() {
    super('styles_mast', 'style_id');
  }

  /**
   * Creates a new style record.
   * Overrides BaseQueries.create to ensure proper data handling if needed,
   * otherwise relies on the base implementation for timestamp management.
   * @param {Tables['styles_mast']['Insert']} data - The data for the new style.
   * @returns {Promise<Tables['styles_mast']['Row']>} The created style record.
   */
  async createStyle(data: Tables['styles_mast']['Insert']): Promise<Tables['styles_mast']['Row']> {
    // Currently, BaseQueries.create handles timestamps correctly.
    // If specific logic for style creation is needed, add it here.
    // Example: data validation specific to styles.
    return this.create(data);
  }

  /**
   * Updates an existing style record.
   * Overrides BaseQueries.update if specific logic is needed.
   * @param {string} styleId - The ID of the style to update.
   * @param {Tables['styles_mast']['Update']} data - The update data.
   * @returns {Promise<Tables['styles_mast']['Row']>} The updated style record.
   */
  async updateStyle(styleId: string, data: Tables['styles_mast']['Update']): Promise<Tables['styles_mast']['Row']> {
    // Currently, BaseQueries.update handles timestamps correctly.
    // If specific logic for style update is needed, add it here.
    return this.update(styleId, data);
  }

  // Add other specific methods for styles_mast if needed, e.g.:
  // async getStylesByItemType(itemTypeId: string): Promise<Tables['styles_mast']['Row'][]> { ... }
}

// Export an instance for easy use
export const styleQueries = new StyleQueries();
