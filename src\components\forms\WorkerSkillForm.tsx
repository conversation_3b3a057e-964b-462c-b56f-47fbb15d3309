'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { type Database } from '@/types/db';

type Worker = Database['public']['Tables']['worker_mast']['Row'];
type Process = Database['public']['Tables']['process_mast']['Row'];
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { toast } from 'react-hot-toast';
import { supabase } from '@/lib/db';

// Define simpler types for dropdown options
interface WorkerOption {
    worker_id: string;
    name: string;
}

interface ProcessOption {
    process_id: string;
    name: string;
}

interface WorkerSkillFormData {
    worker_id: string;
    process_id: string;
    skill_level: number;
    efficiency_factor: number;
    is_primary: boolean;
}

export function WorkerSkillForm() {
    const [workers, setWorkers] = useState<WorkerOption[]>([]);
    const [processes, setProcesses] = useState<ProcessOption[]>([]);
    const [loading, setLoading] = useState(false);

    const { register, handleSubmit, reset, formState: { errors } } = useForm<WorkerSkillFormData>();

    useEffect(() => {
        // Load workers and processes
        fetchWorkers();
        fetchProcesses();
    }, []);

    const fetchWorkers = async () => {
        try {
            const { data, error } = await supabase
                .from('worker_mast')
                .select('worker_id, name')
                .eq('is_active', true)
                .order('name');

            if (error) throw error;
            // Data fetched matches WorkerOption[] now
            setWorkers(data || []);
        } catch (error) {
            console.error('Error fetching workers:', error);
            toast.error('Failed to load workers');
        }
    };

    const fetchProcesses = async () => {
        try {
            const { data, error } = await supabase
                .from('process_mast')
                .select('process_id, name')
                .eq('is_active', true)
                .order('name');

            if (error) throw error;
            // Data fetched matches ProcessOption[] now
            setProcesses(data || []);
        } catch (error) {
            console.error('Error fetching processes:', error);
            toast.error('Failed to load processes');
        }
    };

    const onSubmit = async (data: WorkerSkillFormData) => {
        setLoading(true);
        try {
            const { error } = await supabase
                .from('worker_skills')
                .insert([{
                    worker_id: data.worker_id,
                    process_id: data.process_id,
                    skill_level: data.skill_level,
                    efficiency_factor: data.efficiency_factor,
                    is_primary: data.is_primary,
                }]);

            if (error) throw error;

            toast.success('Worker skill saved successfully');
            reset();
        } catch (error) {
            console.error('Error saving worker skill:', error);
            toast.error('Failed to save worker skill');
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
                <Select
                    label="Worker"
                    {...register('worker_id', { required: 'Worker is required' })}
                    options={[
                        { value: '', label: 'Select Worker' },
                        ...workers.map(worker => ({
                            value: worker.worker_id,
                            label: worker.name
                        }))
                    ]}
                    error={errors.worker_id?.message}
                    className="w-full"
                />
            </div>

            <div>
                <Select
                    label="Process"
                    {...register('process_id', { required: 'Process is required' })}
                    options={[
                        { value: '', label: 'Select Process' },
                        ...processes.map(process => ({
                            value: process.process_id,
                            label: process.name
                        }))
                    ]}
                    error={errors.process_id?.message}
                    className="w-full"
                />
            </div>

            <div>
                <Input
                    label="Skill Level (1-5)"
                    type="number"
                    {...register('skill_level', {
                        required: 'Skill level is required',
                        min: { value: 1, message: 'Minimum skill level is 1' },
                        max: { value: 5, message: 'Maximum skill level is 5' }
                    })}
                    error={errors.skill_level?.message}
                    className="w-full"
                />
            </div>

            <div>
                <Input
                    label="Efficiency Factor"
                    type="number"
                    step="0.1"
                    {...register('efficiency_factor', {
                        required: 'Efficiency factor is required',
                        min: { value: 0.1, message: 'Minimum efficiency factor is 0.1' },
                        max: { value: 2, message: 'Maximum efficiency factor is 2' }
                    })}
                    error={errors.efficiency_factor?.message}
                    className="w-full"
                />
            </div>

            <div>
                <label className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        {...register('is_primary')}
                        className="form-checkbox"
                    />
                    <span className="text-sm font-medium">Primary Skill</span>
                </label>
            </div>

            <Button
                type="submit"
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Worker Skill'}
            </Button>
        </form>
    );
}
