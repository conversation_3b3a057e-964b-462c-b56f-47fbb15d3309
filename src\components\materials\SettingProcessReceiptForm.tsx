/**
 * @component SettingProcessReceiptForm
 * @description Comprehensive stone management for Setting process receipts
 * 
 * BUSINESS SCENARIOS:
 * 1. All stones set successfully (single confirmation)
 * 2. All stones returned unset (single confirmation)
 * 3. Mixed scenario: some set, some returned, some broken, some lost
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DiamondIcon,
  CheckCircleIcon,
  XCircleIcon,
  AlertTriangleIcon,
  RotateCcwIcon,
  EyeOffIcon,
  SettingsIcon
} from 'lucide-react';
import {
  processSettingReceipt,
  StoneDisposition,
  SettingReceiptRequest
} from '@/services/settingProcessService';

interface StoneIssued {
  stoneId: string;
  stoneType: 'diamond' | 'colored_stone' | 'polki';
  shape: string;
  size: string;
  quantity: number;
  carats: number;
  description: string;
  customerMaterial: boolean;
  // Add the required ID fields for proper mapping
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
}

// Local interface for form state - simpler than full StoneDisposition
interface StoneDispositionForm {
  stoneId: string;
  quantitySet: number;
  quantityReturned: number;
  quantityBroken: number;
  quantityLost: number;
  notes?: string;
}

// Use SettingReceiptRequest from service (already imported)

interface SettingProcessReceiptFormProps {
  orderId: string;
  workerId: string;
  processId: string;
  transactionId: string;
  stonesIssued: StoneIssued[];
  previousNetWeight: number;
  onSubmit: (data: SettingReceiptRequest) => void;
}

export function SettingProcessReceiptForm({
  orderId,
  workerId,
  processId,
  transactionId,
  stonesIssued,
  previousNetWeight,
  onSubmit
}: SettingProcessReceiptFormProps) {
  const [scenario, setScenario] = useState<'all_set' | 'all_returned' | 'mixed'>('all_set');
  const [grossWeightReceived, setGrossWeightReceived] = useState(previousNetWeight);
  const [netWeightReceived, setNetWeightReceived] = useState(previousNetWeight);
  const [stoneDispositions, setStoneDispositions] = useState<StoneDispositionForm[]>([]);
  const [notes, setNotes] = useState('');

  // Initialize stone dispositions
  useEffect(() => {
    const initialDispositions = stonesIssued.map(stone => ({
      stoneId: stone.stoneId,
      quantitySet: scenario === 'all_set' ? stone.quantity : 0,
      quantityReturned: scenario === 'all_returned' ? stone.quantity : 0,
      quantityBroken: 0,
      quantityLost: 0,
      notes: ''
    }));
    setStoneDispositions(initialDispositions);
  }, [stonesIssued, scenario]);

  // Calculate expected gross weight
  const totalStonesCarats = stonesIssued.reduce((sum, stone) => sum + stone.carats, 0);
  const totalStonesGrams = totalStonesCarats * 0.2; // 1 carat = 0.2g
  const expectedGrossWeight = previousNetWeight + totalStonesGrams;

  // Calculate stone disposition summary
  const getDispositionSummary = () => {
    const summary = stoneDispositions.reduce((acc, disp) => {
      const stone = stonesIssued.find(s => s.stoneId === disp.stoneId);
      if (!stone) return acc;

      const stoneCarats = (disp.quantitySet / stone.quantity) * stone.carats;
      const returnedCarats = (disp.quantityReturned / stone.quantity) * stone.carats;
      const brokenCarats = (disp.quantityBroken / stone.quantity) * stone.carats;
      const lostCarats = (disp.quantityLost / stone.quantity) * stone.carats;

      return {
        totalSet: acc.totalSet + disp.quantitySet,
        totalReturned: acc.totalReturned + disp.quantityReturned,
        totalBroken: acc.totalBroken + disp.quantityBroken,
        totalLost: acc.totalLost + disp.quantityLost,
        caratsSet: acc.caratsSet + stoneCarats,
        caratsReturned: acc.caratsReturned + returnedCarats,
        caratsBroken: acc.caratsBroken + brokenCarats,
        caratsLost: acc.caratsLost + lostCarats
      };
    }, {
      totalSet: 0,
      totalReturned: 0,
      totalBroken: 0,
      totalLost: 0,
      caratsSet: 0,
      caratsReturned: 0,
      caratsBroken: 0,
      caratsLost: 0
    });

    return summary;
  };

  const updateStoneDisposition = (stoneId: string, updates: Partial<StoneDispositionForm>) => {
    setStoneDispositions(prev =>
      prev.map(disp =>
        disp.stoneId === stoneId ? { ...disp, ...updates } : disp
      )
    );
  };

  const applyQuickScenario = (newScenario: 'all_set' | 'all_returned' | 'mixed') => {
    setScenario(newScenario);

    if (newScenario === 'all_set') {
      // All stones set - gross weight should include all stones
      setGrossWeightReceived(expectedGrossWeight);
      setNetWeightReceived(previousNetWeight);
    } else if (newScenario === 'all_returned') {
      // All stones returned - gross weight same as net weight
      setGrossWeightReceived(previousNetWeight);
      setNetWeightReceived(previousNetWeight);
    }
    // For 'mixed', let user input manually
  };

  const handleScenarioChange = (value: string) => {
    if (value === 'all_set' || value === 'all_returned' || value === 'mixed') {
      applyQuickScenario(value);
    }
  };

  const validateDispositions = () => {
    const errors: string[] = [];
    
    stoneDispositions.forEach(disp => {
      const stone = stonesIssued.find(s => s.stoneId === disp.stoneId);
      if (!stone) return;

      const total = disp.quantitySet + disp.quantityReturned + disp.quantityBroken + disp.quantityLost;
      if (total !== stone.quantity) {
        errors.push(`${stone.description}: Total disposition (${total}) doesn't match issued quantity (${stone.quantity})`);
      }
    });

    return errors;
  };

  const summary = getDispositionSummary();
  const validationErrors = validateDispositions();
  const isValid = validationErrors.length === 0;

  const handleSubmit = async () => {
    if (!isValid) return;

    try {
      // Convert form data to service format
      const serviceDispositions: StoneDisposition[] = stoneDispositions.map(disp => {
        const stone = stonesIssued.find(s => s.stoneId === disp.stoneId);
        return {
          stoneId: disp.stoneId,
          stoneTypeId: stone?.stone_type_id || '',
          stoneShapeId: stone?.stone_shape_id || '',
          stoneSizeId: stone?.stone_size_id || '',
          description: stone?.description || '',
          quantityIssued: stone?.quantity || 0,
          caratsIssued: stone?.carats || 0,
          quantitySet: disp.quantitySet,
          quantityReturned: disp.quantityReturned,
          quantityBroken: disp.quantityBroken,
          quantityLost: disp.quantityLost,
          caratsSet: (disp.quantitySet / (stone?.quantity || 1)) * (stone?.carats || 0),
          caratsReturned: (disp.quantityReturned / (stone?.quantity || 1)) * (stone?.carats || 0),
          caratsBroken: (disp.quantityBroken / (stone?.quantity || 1)) * (stone?.carats || 0),
          caratsLost: (disp.quantityLost / (stone?.quantity || 1)) * (stone?.carats || 0),
          notes: disp.notes
        };
      });

      const data: SettingReceiptRequest = {
        orderId,
        workerId,
        processId,
        transactionId,
        scenario,
        grossWeightReceived,
        netWeightReceived,
        stoneDispositions: serviceDispositions,
        notes,
        receivedBy: 'current_user' // TODO: Get from auth context
      };

      onSubmit(data);
    } catch (error) {
      console.error('Error preparing setting receipt data:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="w-5 h-5" />
            Setting Process Receipt - Order {orderId}
          </CardTitle>
          <CardDescription>
            Worker: {workerId} | {stonesIssued.length} stone types issued
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Quick Scenario Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Stone Setting Scenario</CardTitle>
          <CardDescription>
            Choose the scenario that best describes what happened during setting
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RadioGroup value={scenario} onValueChange={handleScenarioChange}>
            <div className="space-y-4">
              {/* All Set */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-green-50">
                <RadioGroupItem value="all_set" id="all_set" />
                <div className="flex-1">
                  <Label htmlFor="all_set" className="flex items-center gap-2 font-medium cursor-pointer">
                    <CheckCircleIcon className="w-5 h-5 text-green-600" />
                    All Stones Set Successfully
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    All issued stones were successfully set in the product. Single confirmation.
                  </p>
                </div>
              </div>

              {/* All Returned */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-blue-50">
                <RadioGroupItem value="all_returned" id="all_returned" />
                <div className="flex-1">
                  <Label htmlFor="all_returned" className="flex items-center gap-2 font-medium cursor-pointer">
                    <RotateCcwIcon className="w-5 h-5 text-blue-600" />
                    All Stones Returned Unset
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    All stones returned as-is, not set in the product. Single confirmation.
                  </p>
                </div>
              </div>

              {/* Mixed Scenario */}
              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-yellow-50">
                <RadioGroupItem value="mixed" id="mixed" />
                <div className="flex-1">
                  <Label htmlFor="mixed" className="flex items-center gap-2 font-medium cursor-pointer">
                    <AlertTriangleIcon className="w-5 h-5 text-yellow-600" />
                    Mixed Scenario (Detailed Tracking)
                  </Label>
                  <p className="text-sm text-gray-600 mt-1">
                    Some stones set, some returned, some broken/lost. Requires detailed input.
                  </p>
                </div>
              </div>
            </div>
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Product Weights */}
      <Card>
        <CardHeader>
          <CardTitle>Product Weights After Setting</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="gross-weight">Gross Weight (g)</Label>
              <Input
                id="gross-weight"
                type="number"
                step="0.001"
                value={grossWeightReceived}
                onChange={(e) => setGrossWeightReceived(parseFloat(e.target.value) || 0)}
              />
              <div className="text-xs text-gray-500 mt-1">
                Expected if all set: {expectedGrossWeight.toFixed(3)}g
              </div>
            </div>
            <div>
              <Label htmlFor="net-weight">Net Weight (g)</Label>
              <Input
                id="net-weight"
                type="number"
                step="0.001"
                value={netWeightReceived}
                onChange={(e) => setNetWeightReceived(parseFloat(e.target.value) || 0)}
              />
              <div className="text-xs text-gray-500 mt-1">
                Expected: {previousNetWeight.toFixed(3)}g (metal only)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Stone Tracking (for mixed scenario) */}
      {scenario === 'mixed' && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Stone Disposition</CardTitle>
            <CardDescription>
              Track exactly what happened to each stone type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stonesIssued.map((stone) => {
                const disposition = stoneDispositions.find(d => d.stoneId === stone.stoneId);
                if (!disposition) return null;

                return (
                  <Card key={stone.stoneId} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <DiamondIcon className="w-4 h-4" />
                        <span className="font-medium">{stone.description}</span>
                        <Badge variant="outline">
                          {stone.quantity} pcs | {stone.carats.toFixed(2)} carats
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 gap-3">
                      <div>
                        <Label className="text-xs flex items-center gap-1">
                          <CheckCircleIcon className="w-3 h-3 text-green-600" />
                          Set
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max={stone.quantity}
                          value={disposition.quantitySet}
                          onChange={(e) => updateStoneDisposition(stone.stoneId, {
                            quantitySet: parseInt(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs flex items-center gap-1">
                          <RotateCcwIcon className="w-3 h-3 text-blue-600" />
                          Returned
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max={stone.quantity}
                          value={disposition.quantityReturned}
                          onChange={(e) => updateStoneDisposition(stone.stoneId, {
                            quantityReturned: parseInt(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs flex items-center gap-1">
                          <XCircleIcon className="w-3 h-3 text-red-600" />
                          Broken
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max={stone.quantity}
                          value={disposition.quantityBroken}
                          onChange={(e) => updateStoneDisposition(stone.stoneId, {
                            quantityBroken: parseInt(e.target.value) || 0
                          })}
                        />
                      </div>
                      <div>
                        <Label className="text-xs flex items-center gap-1">
                          <EyeOffIcon className="w-3 h-3 text-gray-600" />
                          Lost
                        </Label>
                        <Input
                          type="number"
                          min="0"
                          max={stone.quantity}
                          value={disposition.quantityLost}
                          onChange={(e) => updateStoneDisposition(stone.stoneId, {
                            quantityLost: parseInt(e.target.value) || 0
                          })}
                        />
                      </div>
                    </div>

                    <div className="mt-2">
                      <Label className="text-xs">Notes</Label>
                      <Input
                        placeholder="Additional notes for this stone type..."
                        value={disposition.notes || ''}
                        onChange={(e) => updateStoneDisposition(stone.stoneId, {
                          notes: e.target.value
                        })}
                      />
                    </div>

                    {/* Validation for this stone */}
                    {(() => {
                      const total = disposition.quantitySet + disposition.quantityReturned + 
                                   disposition.quantityBroken + disposition.quantityLost;
                      return total !== stone.quantity && (
                        <Alert className="mt-2">
                          <AlertTriangleIcon className="w-4 h-4" />
                          <AlertDescription>
                            Total ({total}) must equal issued quantity ({stone.quantity})
                          </AlertDescription>
                        </Alert>
                      );
                    })()}
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Stone Disposition Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{summary.totalSet}</div>
              <div className="text-sm text-green-700">Stones Set</div>
              <div className="text-xs text-gray-600">{summary.caratsSet.toFixed(2)} carats</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{summary.totalReturned}</div>
              <div className="text-sm text-blue-700">Returned</div>
              <div className="text-xs text-gray-600">{summary.caratsReturned.toFixed(2)} carats</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{summary.totalBroken}</div>
              <div className="text-sm text-red-700">Broken</div>
              <div className="text-xs text-gray-600">{summary.caratsBroken.toFixed(2)} carats</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{summary.totalLost}</div>
              <div className="text-sm text-gray-700">Lost</div>
              <div className="text-xs text-gray-600">{summary.caratsLost.toFixed(2)} carats</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert>
          <AlertTriangleIcon className="w-4 h-4" />
          <AlertDescription>
            <ul className="list-disc list-inside">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <textarea
            className="w-full h-20 p-3 border rounded-md"
            placeholder="Any additional notes about the setting process..."
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
          />
        </CardContent>
      </Card>

      {/* Submit */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSubmit}
          disabled={!isValid}
          className="w-full"
        >
          Complete Setting Process Receipt
        </Button>
      </div>
    </div>
  );
}
