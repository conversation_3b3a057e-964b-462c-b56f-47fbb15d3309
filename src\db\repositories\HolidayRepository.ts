import { BaseRepository } from './BaseRepository';
import { HolidayCalendar } from '@/types/database';

type Holiday = Omit<HolidayCalendar, 'holiday_id'> & { id: string };

export class HolidayRepository extends BaseRepository<Holiday> {
  protected tableName = 'holiday_calendar';
  protected entityName = 'Holiday';

  async getHolidaysInRange(startDate: string, endDate: string): Promise<Holiday[]> {
    return this.executeQuery<Holiday[]>(async () => {
      const response = await this.supabase
        .from(this.tableName)
        .select('*')
        .gte('holiday_date', startDate)
        .lte('holiday_date', endDate);
      return { data: response.data, error: response.error };
    });
  }

  async addHoliday(holiday: Omit<Holiday, 'holiday_id'>): Promise<Holiday> {
    return this.executeQuery<Holiday>(async () => {
      const response = await this.supabase
        .from(this.tableName)
        .insert(holiday)
        .select()
        .single();
      return { data: response.data, error: response.error };
    });
  }

  async updateHoliday(
    holidayId: string,
    updates: Partial<Holiday>
  ): Promise<Holiday> {
    return this.executeQuery<Holiday>(async () => {
      const response = await this.supabase
        .from(this.tableName)
        .update(updates)
        .eq('holiday_id', holidayId)
        .select()
        .single();
      return { data: response.data, error: response.error };
    });
  }

  async deleteHoliday(holidayId: string): Promise<void> {
    await this.executeQuery<void>(async () => {
      const response = await this.supabase
        .from(this.tableName)
        .delete()
        .eq('holiday_id', holidayId);
      return { data: null, error: response.error };
    });
  }

  async getRecurringHolidays(): Promise<Holiday[]> {
    return this.executeQuery<Holiday[]>(async () => {
      const response = await this.supabase
        .from(this.tableName)
        .select('*')
        .eq('is_recurring', true);
      return { data: response.data, error: response.error };
    });
  }
}
