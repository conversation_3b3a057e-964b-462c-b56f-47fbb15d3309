/**
 * Dust Management Page
 * Comprehensive dust collection and refining management
 * 
 * @module app/materials/dust
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CircleIcon, 
  FlaskConicalIcon,
  TrendingUpIcon,
  PackageIcon
} from 'lucide-react';
import { DustCollectionForm } from '@/components/dust/DustCollectionForm';

interface DustStats {
  total_parcels: number;
  ready_for_refining: number;
  total_weight_grams: number;
  estimated_recovery_grams: number;
  active_batches: number;
  completed_batches: number;
}

export default function DustManagementPage() {
  const dustStats: DustStats = {
    total_parcels: 45,
    ready_for_refining: 15,
    total_weight_grams: 125.8,
    estimated_recovery_grams: 100.6,
    active_batches: 3,
    completed_batches: 12
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Dust Management</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage dust collection, parcel creation, and refining batch operations
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Parcels</p>
                <p className="text-2xl font-bold">{dustStats.total_parcels}</p>
              </div>
              <CircleIcon className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Ready for Refining</p>
                <p className="text-2xl font-bold text-green-600">{dustStats.ready_for_refining}</p>
              </div>
              <FlaskConicalIcon className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Weight</p>
                <p className="text-2xl font-bold">{dustStats.total_weight_grams}g</p>
              </div>
              <TrendingUpIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Est. Recovery</p>
                <p className="text-2xl font-bold text-purple-600">{dustStats.estimated_recovery_grams}g</p>
              </div>
              <TrendingUpIcon className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Active Batches</p>
                <p className="text-2xl font-bold text-orange-600">{dustStats.active_batches}</p>
              </div>
              <PackageIcon className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-gray-600">{dustStats.completed_batches}</p>
              </div>
              <PackageIcon className="w-8 h-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Dust Collection and Refining Forms */}
        <div className="lg:col-span-3">
          <DustCollectionForm />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recovery Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recovery Rates</CardTitle>
              <CardDescription>
                Average recovery by process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Filing</span>
                <Badge variant="secondary">82%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Setting</span>
                <Badge variant="secondary">85%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Polishing</span>
                <Badge variant="secondary">78%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Pre-Polish</span>
                <Badge variant="secondary">80%</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Purity Estimates */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Purity Estimates</CardTitle>
              <CardDescription>
                Average purity by process
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Filing</span>
                <Badge variant="outline">85%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Setting</span>
                <Badge variant="outline">90%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Polishing</span>
                <Badge variant="outline">95%</Badge>
              </div>
              <div className="flex items-center justify-between p-2 rounded-lg bg-gray-50 dark:bg-gray-800">
                <span className="text-sm">Pre-Polish</span>
                <Badge variant="outline">88%</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Refining Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Refining Tips</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                <div className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                  Batch Size
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-300">
                  Optimal batch size is 50-100g for best recovery rates
                </div>
              </div>
              
              <div className="p-3 rounded-lg bg-green-50 dark:bg-green-900/20">
                <div className="text-sm font-medium text-green-800 dark:text-green-200 mb-1">
                  Customer Segregation
                </div>
                <div className="text-xs text-green-600 dark:text-green-300">
                  Keep customer materials separate throughout the refining process
                </div>
              </div>
              
              <div className="p-3 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  Recovery Tracking
                </div>
                <div className="text-xs text-yellow-600 dark:text-yellow-300">
                  Track actual vs expected recovery to improve estimates
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">This Month</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Dust Collected</span>
                <span className="font-semibold">245.6g</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Metal Recovered</span>
                <span className="font-semibold">198.2g</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Recovery Rate</span>
                <span className="font-semibold text-green-600">80.7%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Batches Processed</span>
                <span className="font-semibold">8</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
