'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';

interface ItemTypeFormData {
    description: string;
    average_processing_time: number;
    suffix: string;
}

export function ItemTypeForm() {
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();

    const { register, handleSubmit, reset, formState: { errors } } = useForm<ItemTypeFormData>();

    const onSubmit = async (data: ItemTypeFormData) => {
        setLoading(true);
        try {
            const response = await fetch('/api/item-types', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) throw new Error('Failed to save item type');

            toast({
                title: 'Success',
                description: 'Item type saved successfully',
                variant: 'default'
            });
            reset();
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save item type',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <Input
                    type="text"
                    {...register('description', { required: 'Description is required' })}
                    className="w-full"
                />
                {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2">Average Processing Time (hours)</label>
                <Input
                    type="number"
                    step="0.5"
                    {...register('average_processing_time', {
                        required: 'Processing time is required',
                        min: { value: 0.5, message: 'Minimum 0.5 hours' }
                    })}
                    className="w-full"
                />
                {errors.average_processing_time && (
                    <p className="text-red-500 text-sm mt-1">{errors.average_processing_time.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2">Suffix</label>
                <Input
                    type="text"
                    {...register('suffix', {
                        required: 'Suffix is required',
                        pattern: {
                            value: /^[A-Z0-9]+$/,
                            message: 'Suffix must be uppercase letters and numbers only'
                        }
                    })}
                    className="w-full"
                />
                {errors.suffix && (
                    <p className="text-red-500 text-sm mt-1">{errors.suffix.message}</p>
                )}
            </div>

            <Button
                type="submit"
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Item Type'}
            </Button>
        </form>
    );
}
