'use client';

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/db';

interface UseMasterDataProps<T> {
  tableName: string;
  idField?: string;
}

export function useMasterData<T extends { [key: string]: any }>({
  tableName,
  idField = `${tableName.replace('_mast', '')}_id`,
}: UseMasterDataProps<T>) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch data from Supabase
  const fetchData = async () => {
    try {
      console.log('Fetching data from table:', tableName, 'with idField:', idField);
      setLoading(true);
      setError(null); // Reset error state

      // Check if we have a valid Supabase client
      if (!supabase) {
        throw new Error('Supabase client is not initialized');
      }

      // Perform the query
      const { data: fetchedData, error: fetchError } = await supabase
        .from(tableName)
        .select('*');

      // Log the results
      console.log('Fetch response for', tableName, ':', {
        data: fetchedData,
        error: fetchError,
      });

      if (fetchError) {
        console.error('Supabase error:', fetchError);
        throw new Error(fetchError.message);
      }

      setData(fetchedData || []);
    } catch (err) {
      console.error('Error fetching data from', tableName, ':', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch data'));
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [tableName]);

  const add = async (item: Omit<T, typeof idField>) => {
    try {
      console.log('Adding item to table:', tableName, item);
      const { data: newItem, error } = await supabase
        .from(tableName)
        .insert([item])
        .select()
        .single();

      console.log('Add result:', newItem, 'Error:', error);

      if (error) throw error;
      if (newItem) {
        setData((prev) => [...prev, newItem as T]);
      }
      return newItem;
    } catch (err) {
      console.error('Error adding item:', err);
      setError(err instanceof Error ? err : new Error('Failed to add item'));
      throw err;
    }
  };

  const update = async (id: string, updates: Partial<T>) => {
    try {
      // Remove the ID field and timestamps from updates to prevent conflicts
      const { [idField]: _id, created_at, updated_at, ...cleanUpdates } = updates;

      console.log('Updating item in table:', tableName, {
        id,
        idField,
        updates: cleanUpdates,
      });

      const { data: updatedItem, error } = await supabase
        .from(tableName)
        .update(cleanUpdates)
        .eq(idField, id)
        .select()
        .single();

      console.log('Update response:', {
        data: updatedItem,
        error,
      });

      if (error) throw error;
      if (updatedItem) {
        setData((prev) =>
          prev.map((item) => (item[idField] === id ? { ...item, ...updatedItem } : item))
        );
      }
      return updatedItem;
    } catch (err) {
      console.error('Error updating item:', err);
      setError(err instanceof Error ? err : new Error('Failed to update item'));
      throw err;
    }
  };

  const remove = async (id: string) => {
    try {
      console.log('Removing item from table:', tableName, 'id:', id);
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq(idField, id);

      if (error) throw error;
      setData((prev) => prev.filter((item) => item[idField] !== id));
    } catch (err) {
      console.error('Error removing item:', err);
      setError(err instanceof Error ? err : new Error('Failed to remove item'));
      throw err;
    }
  };

  return {
    data,
    loading,
    error,
    add,
    update,
    remove,
    refresh: fetchData,
  };
}
