/**
 * Store Types Module
 * Defines TypeScript interfaces for all Zustand stores
 * @module store/types
 */

// import { User, UserRole } from '@/types/common'; // Commented out temporarily - Module not found
import { Process, ProcessTracking } from '@/types/process';
import { Worker } from '@/types/domain/worker.types';
// import { Order } from '@/types/orders'; // Use OrderWithDetails for state
import { OrderWithDetails } from '@/db/queries/orders'; // Import OrderWithDetails
import { OrderInsert, OrderUpdate } from '@/types/database'; // Import DB operation types

/**
 * Authentication Store Interface
 * Manages user authentication state and operations
 * 
 * @interface AuthStore
 * @property {User | null} user - Currently authenticated user
 * @property {UserRole | null} userRole - Role of the current user
 * @property {boolean} loading - Loading state for auth operations
 * @property {boolean} isAuthenticated - Whether a user is currently authenticated
 */
export interface AuthStore {
  user: any | null;
  userRole: any | null;
  loading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, role?: any) => Promise<void>;
  signOut: () => Promise<void>;
  setUser: (user: any | null) => void;
  setUserRole: (role: any | null) => void;
}

/**
 * Process Store Interface
 * Manages manufacturing process state and operations
 * 
 * @interface ProcessStore
 * @property {Process[]} processes - List of all processes
 * @property {ProcessTracking[]} activeProcesses - List of currently active processes
 * @property {ProcessTracking | null} selectedProcess - Currently selected process tracking record
 * @property {boolean} loading - Loading state for process operations
 * @property {Error | null} error - Error state for process operations
 */
export interface ProcessStore {
  processes: Process[];
  activeProcesses: ProcessTracking[];
  selectedProcess: ProcessTracking | null;
  loading: boolean;
  error: Error | null;
  fetchProcesses: () => Promise<void>;
  startProcess: (trackingId: string) => Promise<void>;
  completeProcess: (trackingId: string) => Promise<void>;
  updateProcess: (trackingId: string, data: Partial<Omit<ProcessTracking, 'id' | 'created_at' | 'updated_at'>>) => Promise<void>;
  setSelectedProcess: (process: ProcessTracking | null) => void;
}

/**
 * Worker Store Interface
 * Manages workshop worker state and operations
 * 
 * @interface WorkerStore
 * @property {Worker[]} workers - List of all workers
 * @property {boolean} loading - Loading state for worker operations
 * @property {Error | null} error - Error state for worker operations
 */
export interface WorkerStore {
  workers: Worker[];
  loading: boolean;
  error: Error | null;
  fetchWorkers: () => Promise<void>;
  addWorker: (worker: Omit<Worker, 'worker_id'>) => Promise<void>;
  updateWorker: (id: string, data: Partial<Worker>) => Promise<void>;
  deleteWorker: (id: string) => Promise<void>;
}

/**
 * Order Store Interface
 * Manages manufacturing order state and operations
 * 
 * @interface OrderStore
 * @property {Order[]} orders - List of all orders
 * @property {boolean} loading - Loading state for order operations
 * @property {Error | null} error - Error state for order operations
 */
export interface OrderStore {
  orders: OrderWithDetails[]; // Changed state type
  loading: boolean;
  error: Error | null;
  fetchOrders: () => Promise<void>;
  addOrder: (order: OrderInsert) => Promise<void>; // Changed parameter type
  updateOrder: (id: string, data: Partial<OrderUpdate>) => Promise<void>; // Changed parameter type
  deleteOrder: (id: string) => Promise<void>;
}
