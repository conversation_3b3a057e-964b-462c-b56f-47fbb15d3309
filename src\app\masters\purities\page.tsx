'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Search, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/lib/db';
import { PurityMaster, MetalTypeMaster } from '@/types/masters';

export default function PuritiesPage() {
  const [purities, setPurities] = useState<PurityMaster[]>([]);
  const [metalTypes, setMetalTypes] = useState<MetalTypeMaster[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMetalType, setSelectedMetalType] = useState<string>('all');
  const [showForm, setShowForm] = useState(false);
  const [editingPurity, setEditingPurity] = useState<PurityMaster | null>(null);
  const [formData, setFormData] = useState({
    metal_type_id: '',
    description: '',
    purity_percentage: 0,
    standard_wastage_percentage: 0,
    density: 0,
    is_active: true,
  });
  const { toast } = useToast();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch metal types
      const { data: metalTypesData, error: metalTypesError } = await supabase
        .from('metal_type_mast')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (metalTypesError) throw metalTypesError;
      setMetalTypes(metalTypesData || []);

      // Fetch purities with metal type information
      const { data: puritiesData, error: puritiesError } = await supabase
        .from('purity_mast')
        .select(`
          *,
          metal_type:metal_type_mast(
            metal_type_id,
            name,
            description
          )
        `)
        .order('metal_type_id, purity_percentage');

      if (puritiesError) throw puritiesError;
      setPurities(puritiesData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (editingPurity) {
        // Update existing purity
        const { error } = await supabase
          .from('purity_mast')
          .update({
            ...formData,
            updated_at: new Date().toISOString(),
          })
          .eq('purity_id', editingPurity.purity_id);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Purity updated successfully',
        });
      } else {
        // Create new purity
        const { error } = await supabase
          .from('purity_mast')
          .insert([{
            ...formData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }]);

        if (error) throw error;
        toast({
          title: 'Success',
          description: 'Purity created successfully',
        });
      }

      setShowForm(false);
      setEditingPurity(null);
      setFormData({
        metal_type_id: '',
        description: '',
        purity_percentage: 0,
        standard_wastage_percentage: 0,
        density: 0,
        is_active: true,
      });
      fetchData();
    } catch (error) {
      console.error('Error saving purity:', error);
      toast({
        title: 'Error',
        description: 'Failed to save purity',
        variant: 'destructive',
      });
    }
  };

  const handleEdit = (purity: PurityMaster) => {
    setEditingPurity(purity);
    setFormData({
      metal_type_id: purity.metal_type_id,
      description: purity.description,
      purity_percentage: purity.purity_percentage,
      standard_wastage_percentage: purity.standard_wastage_percentage,
      density: purity.density,
      is_active: purity.is_active,
    });
    setShowForm(true);
  };

  const handleDelete = async (purityId: string) => {
    if (!confirm('Are you sure you want to delete this purity?')) return;

    try {
      const { error } = await supabase
        .from('purity_mast')
        .delete()
        .eq('purity_id', purityId);

      if (error) throw error;
      toast({
        title: 'Success',
        description: 'Purity deleted successfully',
      });
      fetchData();
    } catch (error) {
      console.error('Error deleting purity:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete purity',
        variant: 'destructive',
      });
    }
  };

  const filteredPurities = purities.filter(purity => {
    const matchesSearch = purity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         purity.metal_type?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedMetalType === 'all' || purity.metal_type_id === selectedMetalType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Metal Purities Management</h1>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Purity
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search purities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={selectedMetalType}
          onChange={(e) => setSelectedMetalType(e.target.value)}
          className="w-48 h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">All Metal Types</option>
          {metalTypes.map((type) => (
            <option key={type.metal_type_id} value={type.metal_type_id}>
              {type.name}
            </option>
          ))}
        </select>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>{editingPurity ? 'Edit Purity' : 'Add New Purity'}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Metal Type *</label>
                  <select
                    value={formData.metal_type_id}
                    onChange={(e) => setFormData({ ...formData, metal_type_id: e.target.value })}
                    required
                    className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select metal type</option>
                    {metalTypes.map((type) => (
                      <option key={type.metal_type_id} value={type.metal_type_id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Description *</label>
                  <Input
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="e.g., 22KT, 18KT, 925 Sterling, 950 Platinum"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Purity Percentage *</label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0.01"
                    max="100"
                    value={formData.purity_percentage}
                    onChange={(e) => setFormData({ ...formData, purity_percentage: Number(e.target.value) })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Standard Wastage %</label>
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.standard_wastage_percentage}
                    onChange={(e) => setFormData({ ...formData, standard_wastage_percentage: Number(e.target.value) })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Density (g/cm³)</label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.density}
                    onChange={(e) => setFormData({ ...formData, density: Number(e.target.value) })}
                  />
                </div>
              </div>
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    className="mr-2"
                  />
                  Active
                </label>
              </div>
              <div className="flex space-x-2">
                <Button type="submit">
                  {editingPurity ? 'Update' : 'Create'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setShowForm(false);
                    setEditingPurity(null);
                    setFormData({
                      metal_type_id: '',
                      description: '',
                      purity_percentage: 0,
                      standard_wastage_percentage: 0,
                      density: 0,
                      is_active: true,
                    });
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4">
        {filteredPurities.map((purity) => (
          <Card key={purity.purity_id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-semibold">{purity.description}</h3>
                    <Badge variant="secondary">
                      {purity.metal_type?.name || 'Unknown Metal'}
                    </Badge>
                    <Badge variant={purity.is_active ? 'default' : 'secondary'}>
                      {purity.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                    <div>
                      <span className="font-medium">Purity:</span> {purity.purity_percentage}%
                    </div>
                    <div>
                      <span className="font-medium">Wastage:</span> {purity.standard_wastage_percentage}%
                    </div>
                    <div>
                      <span className="font-medium">Density:</span> {purity.density} g/cm³
                    </div>
                  </div>
                  {purity.metal_type?.description && (
                    <p className="text-sm text-gray-400 mt-2">
                      {purity.metal_type.description}
                    </p>
                  )}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(purity)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(purity.purity_id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPurities.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No purities found</p>
        </div>
      )}
    </div>
  );
}
