/**
 * Order Management Store
 * Manages state for manufacturing orders using Zustand
 * @module store/orderStore
 */

import { create } from 'zustand';
import { OrderStore } from './types';
import { OrderQueries, OrderWithDetails } from '@/db/queries/orders'; // Import OrderWithDetails
// Import OrderInsert and OrderUpdate types from database types
import { OrderInsert, OrderUpdate } from '@/types/database'; 

/**
 * Order queries instance for database operations
 */
const orderQueries = new OrderQueries();

/**
 * Order store using Zustand
 * Handles order state management and CRUD operations
 * 
 * @example
 * ```typescript
 * // Using the order store in a component
 * const { orders, addOrder, updateOrder } = useOrderStore();
 * 
 * // Add a new order
 * await addOrder({
 *   customer_id: 'C123',
 *   item_type: 'RING',
 *   quantity: 5,
 *   due_date: new Date('2025-01-15')
 * });
 * 
 * // Update an order
 * await updateOrder('O123', {
 *   quantity: 7,
 *   priority: 'high'
 * });
 * ```
 */
// Use the updated OrderStore interface
export const useOrderStore = create<OrderStore>((set) => ({ 
  orders: [], // Initial state is still OrderWithDetails[]
  loading: false,
  error: null,

  /**
   * Fetches all orders from the database
   * Updates the store with the fetched orders
   * 
   * @throws {Error} If order fetching fails
   * 
   * @example
   * ```typescript
   * await fetchOrders();
   * // Store now contains updated order list
   * ```
   */
  fetchOrders: async () => {
    set({ loading: true });
    try {
      const orders = await orderQueries.getOrders();
      // Set state, type matches OrderStore interface now
      set({ orders: orders, loading: false }); 
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Adds a new order to the system
   * 
   * @param {Order} order - Order data to create
   * @throws {Error} If order creation fails
   * 
   * @example
   * ```typescript
   * await addOrder({
   *   customer_id: 'C123',
   *   item_type: 'BRACELET',
   *   quantity: 3,
   *   due_date: new Date('2025-02-01'),
   *   priority: 'medium'
   * });
   * ```
   */
  // Explicitly type the order parameter as OrderInsert
  addOrder: async (order: OrderInsert) => { 
    set({ loading: true });
    try {
      await orderQueries.create(order);
      const orders = await orderQueries.getOrders();
      // Set state, type matches OrderStore interface now
      set({ orders: orders, loading: false }); 
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Updates an existing order's information
   * 
   * @param {string} id - ID of the order to update
   * @param {Partial<Order>} data - Updated order data
   * @throws {Error} If order update fails
   * 
   * @example
   * ```typescript
   * await updateOrder('O123', {
   *   quantity: 4,
   *   priority: 'high',
   *   notes: 'Customer requested rush order'
   * });
   * ```
   */
  // Add explicit types for id and data based on OrderStore interface
  updateOrder: async (id: string, data: Partial<OrderUpdate>) => { 
    set({ loading: true });
    try {
      // Pass data directly, type matches OrderUpdate now
      await orderQueries.update(id, data); 
      const orders = await orderQueries.getOrders();
      // Set state, type matches OrderStore interface now
      set({ orders: orders, loading: false }); 
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },

  /**
   * Deletes an order from the system
   * Note: This should only be used for orders that haven't started processing
   * 
   * @param {string} id - ID of the order to delete
   * @throws {Error} If order deletion fails
   * 
   * @example
   * ```typescript
   * await deleteOrder('O123');
   * ```
   */
  // Add explicit type for id
  deleteOrder: async (id: string) => { 
    set({ loading: true });
    try {
      await orderQueries.delete(id);
      const orders = await orderQueries.getOrders();
      // Set state, type matches OrderStore interface now
      set({ orders: orders, loading: false }); 
    } catch (error) {
      set({ error: error as Error, loading: false });
    }
  },
}));
