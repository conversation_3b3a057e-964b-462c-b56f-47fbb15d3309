/**
 * Location Transfers Page
 * Transfer materials between different locations
 * 
 * @module app/materials/transfers
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  WorkflowIcon,
  ArrowRightIcon,
  Building2Icon,
  PackageIcon
} from 'lucide-react';

interface LocationInventory {
  location: string;
  stones: number;
  findings: number;
  metals: number;
  total_items: number;
}

export default function LocationTransfersPage() {
  const [locationInventory] = useState<LocationInventory[]>([
    {
      location: 'Safe',
      stones: 45,
      findings: 12,
      metals: 8,
      total_items: 65
    },
    {
      location: 'Central',
      stones: 78,
      findings: 25,
      metals: 15,
      total_items: 118
    },
    {
      location: 'Floor',
      stones: 32,
      findings: 8,
      metals: 22,
      total_items: 62
    },
    {
      location: 'Customers',
      stones: 156,
      findings: 45,
      metals: 35,
      total_items: 236
    }
  ]);

  const getLocationIcon = (location: string) => {
    return <Building2Icon className="w-5 h-5" />;
  };

  const getLocationColor = (location: string) => {
    switch (location) {
      case 'Safe':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Central':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Floor':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Customers':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Location Transfers</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Transfer materials between different storage locations
        </p>
      </div>

      {/* Location Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {locationInventory.map((location) => (
          <Card key={location.location} className={`border-2 ${getLocationColor(location.location)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  {getLocationIcon(location.location)}
                  <h3 className="font-semibold">{location.location}</h3>
                </div>
                <Badge variant="secondary">{location.total_items} items</Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Stones:</span>
                  <span className="font-medium">{location.stones}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Findings:</span>
                  <span className="font-medium">{location.findings}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Metals:</span>
                  <span className="font-medium">{location.metals}</span>
                </div>
              </div>
              
              <Button variant="outline" className="w-full mt-4">
                View Details
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Transfer Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Quick Transfer */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <WorkflowIcon className="w-5 h-5" />
              Quick Transfer
            </CardTitle>
            <CardDescription>
              Transfer materials between locations quickly
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12 text-gray-500">
              <PackageIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="mb-4">Quick transfer functionality coming soon</p>
              <p className="text-sm">This will allow you to transfer materials between locations with a simple drag-and-drop interface</p>
            </div>
          </CardContent>
        </Card>

        {/* Transfer Rules */}
        <Card>
          <CardHeader>
            <CardTitle>Transfer Rules</CardTitle>
            <CardDescription>
              Guidelines for material location transfers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Customer Materials</h4>
              <p className="text-sm text-blue-600 dark:text-blue-300">
                Customer materials can be moved to Central, Safe, or Floor locations for processing
              </p>
            </div>
            
            <div className="p-4 rounded-lg bg-green-50 dark:bg-green-900/20">
              <h4 className="font-medium text-green-800 dark:text-green-200 mb-2">Safe Storage</h4>
              <p className="text-sm text-green-600 dark:text-green-300">
                High-value items should be stored in Safe location when not in active use
              </p>
            </div>
            
            <div className="p-4 rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Floor Operations</h4>
              <p className="text-sm text-yellow-600 dark:text-yellow-300">
                Materials on Floor are actively being processed by workers
              </p>
            </div>
            
            <div className="p-4 rounded-lg bg-purple-50 dark:bg-purple-900/20">
              <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-2">Central Hub</h4>
              <p className="text-sm text-purple-600 dark:text-purple-300">
                Central location serves as the main distribution point for materials
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transfers */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Recent Transfers</CardTitle>
          <CardDescription>
            Latest material location transfers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg border">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-purple-100 text-purple-800">Customers</Badge>
                  <ArrowRightIcon className="w-4 h-4 text-gray-400" />
                  <Badge className="bg-blue-100 text-blue-800">Central</Badge>
                </div>
                <div>
                  <p className="font-medium">25 pcs Diamond - Round - 2mm</p>
                  <p className="text-sm text-gray-600">Customer A materials</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">2 hours ago</p>
                <p className="text-sm text-gray-500">by John Doe</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 rounded-lg border">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-blue-100 text-blue-800">Central</Badge>
                  <ArrowRightIcon className="w-4 h-4 text-gray-400" />
                  <Badge className="bg-green-100 text-green-800">Safe</Badge>
                </div>
                <div>
                  <p className="font-medium">Polki Assembly - 15.2g</p>
                  <p className="text-sm text-gray-600">High-value finding</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">4 hours ago</p>
                <p className="text-sm text-gray-500">by Jane Smith</p>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 rounded-lg border">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Badge className="bg-green-100 text-green-800">Safe</Badge>
                  <ArrowRightIcon className="w-4 h-4 text-gray-400" />
                  <Badge className="bg-yellow-100 text-yellow-800">Floor</Badge>
                </div>
                <div>
                  <p className="font-medium">18.5g Gold - 22K</p>
                  <p className="text-sm text-gray-600">For polishing process</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">6 hours ago</p>
                <p className="text-sm text-gray-500">by Mike Johnson</p>
              </div>
            </div>
          </div>

          <div className="mt-6 text-center">
            <Button variant="outline">View All Transfers</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
