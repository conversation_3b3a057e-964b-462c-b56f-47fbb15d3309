/**
 * CustomerMaterialReceiptForm Component
 * Form for recording materials received from customers
 * 
 * @module components/inventory/receipt
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { type Database } from '@/types/db';
import { customerMaterialReceiptFields } from '@/components/masters/forms/FormFields';

type CustomerMaterialReceipt = Database['public']['Tables']['customer_material_receipt']['Row'];
import { useToast } from '@/hooks/useToast';

// Interface for the form's select options
interface SelectOption {
  value: string;
  label: string;
}

/**
 * Props for CustomerMaterialReceiptForm component
 */
interface CustomerMaterialReceiptFormProps {
  initialData?: Partial<CustomerMaterialReceipt>;
  onSubmit?: (data: Partial<CustomerMaterialReceipt>) => void;
  onCancel?: () => void;
}

/**
 * CustomerMaterialReceiptForm Component
 * Form for recording materials received from customers
 */
export const CustomerMaterialReceiptForm: React.FC<CustomerMaterialReceiptFormProps> = ({
  initialData,
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState<Partial<CustomerMaterialReceipt>>(initialData || {});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customers, setCustomers] = useState<SelectOption[]>([]);
  const [orders, setOrders] = useState<SelectOption[]>([]);
  const [loadingCustomers, setLoadingCustomers] = useState(true);
  const [loadingOrders, setLoadingOrders] = useState(false);
  // Material type options
  const [metalTypes, setMetalTypes] = useState<SelectOption[]>([]);
  const [stoneTypes, setStoneTypes] = useState<SelectOption[]>([]);
  const [loadingMetalTypes, setLoadingMetalTypes] = useState(true);
  const [loadingStoneTypes, setLoadingStoneTypes] = useState(true);
  const [stoneShapes, setStoneShapes] = useState<SelectOption[]>([]);
  const [loadingStoneShapes, setLoadingStoneShapes] = useState(true);
  
  // Material categories
  const MATERIAL_CATEGORIES = [
    { value: 'metal', label: 'Metal' },
    { value: 'stone', label: 'Stone' },
    { value: 'diamond', label: 'Diamond' },
    { value: 'other', label: 'Other' }
  ];
  
  // Materials being added
  const [materials, setMaterials] = useState<Array<{
    id: string;
    category: string;
    material_type_id: string;
    weight: number;
    purity?: string;
    quantity?: number;
    size_mm?: number;
    shape_id?: string;
    description: string;
  }>>([]);
  const { showToast } = useToast();
  const router = useRouter();

  // Fetch customers and material types on component mount
  useEffect(() => {
    fetchCustomers();
    fetchMetalTypes();
    fetchStoneTypes();
    fetchStoneShapes();
  }, []);

  // Fetch orders when customer is selected
  useEffect(() => {
    if (formData.customer_id) {
      fetchOrders(formData.customer_id);
    } else {
      setOrders([]);
    }
  }, [formData.customer_id]);

  /**
   * Fetches customers from the API
   */
  const fetchCustomers = async () => {
    try {
      setLoadingCustomers(true);
      const response = await fetch('/api/masters/customers');
      
      if (!response.ok) {
        throw new Error('Failed to fetch customers');
      }
      
      const data = await response.json();
      const options = data.map((customer: any) => ({
        value: customer.customer_id,
        label: customer.description
      }));
      
      setCustomers(options);
    } catch (error) {
      console.error('Error fetching customers:', error);
      showToast({
        title: 'Error',
        description: 'Error loading customers',
        type: 'destructive'
      });
    } finally {
      setLoadingCustomers(false);
    }
  };

  /**
   * Fetches metal types from the API
   */
  const fetchMetalTypes = async () => {
    try {
      setLoadingMetalTypes(true);
      const response = await fetch('/api/masters?table=metal_type_mast');
      
      if (!response.ok) {
        throw new Error('Failed to fetch metal types');
      }
      
      const data = await response.json();
      const options = data.map((type: any) => ({
        value: type.metal_type_id,
        label: type.name
      }));
      
      setMetalTypes(options);
    } catch (error) {
      console.error('Error fetching metal types:', error);
      showToast({
        title: 'Error',
        description: 'Error loading metal types',
        type: 'destructive'
      });
    } finally {
      setLoadingMetalTypes(false);
    }
  };

  /**
   * Fetches stone types from the API
   */
  const fetchStoneTypes = async () => {
    try {
      setLoadingStoneTypes(true);
      const response = await fetch('/api/masters?table=stone_type_mast');
      
      if (!response.ok) {
        throw new Error('Failed to fetch stone types');
      }
      
      const data = await response.json();
      const options = data.map((type: any) => ({
        value: type.stone_type_id,
        label: type.name
      }));
      
      setStoneTypes(options);
    } catch (error) {
      console.error('Error fetching stone types:', error);
      showToast({
        title: 'Error',
        description: 'Error loading stone types',
        type: 'destructive'
      });
    } finally {
      setLoadingStoneTypes(false);
    }
  };

  /**
   * Fetches stone shapes from the API
   */
  const fetchStoneShapes = async () => {
    try {
      setLoadingStoneShapes(true);
      const response = await fetch('/api/masters?table=stone_shape_mast');
      
      if (!response.ok) {
        throw new Error('Failed to fetch stone shapes');
      }
      
      const data = await response.json();
      const options = data.map((shape: any) => ({
        value: shape.shape_id,
        label: shape.name
      }));
      
      setStoneShapes(options);
    } catch (error) {
      console.error('Error fetching stone shapes:', error);
      showToast({
        title: 'Error',
        description: 'Error loading stone shapes',
        type: 'destructive'
      });
    } finally {
      setLoadingStoneShapes(false);
    }
  };

  /**
   * Fetches orders for a specific customer
   * @param customerId - The customer ID to fetch orders for
   */
  const fetchOrders = async (customerId: string) => {
    try {
      console.log('Fetching orders for customer ID:', customerId);
      setLoadingOrders(true);
      const url = `/api/orders?customerId=${customerId}`;
      console.log('Fetching from URL:', url);
      const response = await fetch(url);
      
      if (!response.ok) {
        console.error('Response not OK:', response.status, response.statusText);
        throw new Error(`Failed to fetch orders: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Orders data received:', data);
      
      if (!Array.isArray(data)) {
        console.error('Expected array of orders but got:', typeof data, data);
        setOrders([]);
        return;
      }
      
      const options = data.map((order: any) => {
        console.log('Processing order:', order);
        return {
          value: order.order_id,
          label: `${order.order_id} - ${order.style_code || 'No Style'}`
        };
      });
      
      console.log('Order options created:', options);
      setOrders(options);
    } catch (error) {
      console.error('Error fetching orders:', error);
      showToast({
        title: 'Error',
        description: 'Error loading orders',
        type: 'destructive'
      });
    } finally {
      setLoadingOrders(false);
    }
  };

  /**
   * Handles form input changes
   * @param e - The input change event
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'date' ? new Date(value).toISOString() : value
    }));
  };

  /**
   * Adds a new material to the list
   * @param category - The category of material to add
   */
  const addMaterial = (category: string = 'metal') => {
    setMaterials(prev => [
      ...prev,
      {
        id: Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15),
        category,
        material_type_id: '',
        weight: 0,
        ...(category === 'metal' ? { purity: '' } : {}),
        ...(category === 'stone' || category === 'diamond' ? { quantity: 0, size_mm: 0, shape_id: '' } : {}),
        description: ''
      }
    ]);
  };

  /**
   * Removes a material from the list
   * @param id - ID of the material to remove
   */
  const removeMaterial = (id: string) => {
    setMaterials(prev => prev.filter(material => material.id !== id));
  };

  /**
   * Handles changes to material fields
   * @param id - ID of the material to update
   * @param field - Field name to update
   * @param value - New value for the field
   */
  const handleMaterialChange = (id: string, field: string, value: string | number) => {
    setMaterials(prev => prev.map(material => {
      if (material.id === id) {
        // If changing category, reset type-specific fields
        if (field === 'category') {
          const category = value as string;
          const baseFields = {
            id: material.id,
            category,
            material_type_id: '',
            weight: material.weight || 0,
            description: material.description || ''
          };
          
          // Add category-specific fields
          if (category === 'metal') {
            return { ...baseFields, purity: '' };
          } else if (category === 'stone' || category === 'diamond') {
            return { 
              ...baseFields, 
              quantity: 0, 
              size_mm: 0,
              shape_id: ''
            };
          } else {
            return baseFields;
          }
        }
        
        // Normal field update
        return { ...material, [field]: value };
      }
      return material;
    }));
  };

  /**
   * Handles form submission
   * @param e - The form submit event
   */
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate materials
      if (materials.length === 0) {
        throw new Error('At least one material must be added');
      }

      // Validate each material
      for (const material of materials) {
        if (!material.material_type_id) {
          throw new Error('Material type is required for all materials');
        }
        if (material.weight <= 0) {
          throw new Error('Weight must be greater than zero for all materials');
        }
      }
      
      // Set the receipt date if not provided
      if (!formData.receipt_date) {
        formData.receipt_date = new Date().toISOString();
      }
      
      // Prepare the complete data with materials
      const completeData = {
        ...formData,
        materials: materials.map(m => ({
          material_type_id: m.material_type_id,
          weight: m.weight,
          purity: m.purity,
          description: m.description
        }))
      };
      
      // If onSubmit callback is provided, use it
      if (onSubmit) {
        onSubmit(completeData);
        return;
      }
      
      // Otherwise, submit to the API directly
      const method = initialData?.receipt_id ? 'PUT' : 'POST';
      const url = '/api/inventory/material-receipts';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(completeData)
      });
      
      if (!response.ok) {
        throw new Error('Failed to save material receipt');
      }
      
      showToast({
        title: 'Success',
        description: `Material receipt ${initialData?.receipt_id ? 'updated' : 'created'} successfully`,
        type: 'success'
      });
      
      // Redirect to materials list
      router.push('/inventory/material-receipt');
    } catch (error) {
      console.error('Error saving material receipt:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      showToast({
        title: 'Error',
        description: errorMessage || 'Error saving material receipt',
        type: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handles cancellation of the form
   */
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.push('/inventory/material-receipt');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-6">
        {initialData?.receipt_id ? 'Edit' : 'New'} Material Receipt
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Customer Field */}
          <div>
            <label htmlFor="customer_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Customer <span className="text-red-500">*</span>
            </label>
            <select
              id="customer_id"
              name="customer_id"
              value={formData.customer_id || ''}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              required
              disabled={loadingCustomers || isSubmitting}
            >
              <option value="">Select Customer</option>
              {customers.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          {/* Order Field - Optional */}
          <div>
            <label htmlFor="order_id" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Order (Optional)
            </label>
            <select
              id="order_id"
              name="order_id"
              value={formData.order_id || ''}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              disabled={!formData.customer_id || loadingOrders || isSubmitting}
            >
              <option value="">Select Order</option>
              {orders.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          
          {/* Receipt Date Field */}
          <div>
            <label htmlFor="receipt_date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Receipt Date <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="receipt_date"
              name="receipt_date"
              value={formData.receipt_date ? new Date(formData.receipt_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              required
              disabled={isSubmitting}
            />
          </div>
          
          {/* Notes Field */}
          <div className="md:col-span-2">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              rows={3}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
              placeholder="Additional information about this receipt"
              disabled={isSubmitting}
            />
          </div>
        </div>

        {/* Materials Section */}
        <div className="mt-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Materials</h3>
            <div className="flex gap-2">
              {MATERIAL_CATEGORIES.map((category) => (
                <button
                  key={category.value}
                  type="button"
                  onClick={() => addMaterial(category.value)}
                  className="px-3 py-1 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={isSubmitting}
                >
                  Add {category.label}
                </button>
              ))}
            </div>
          </div>

          {materials.length === 0 && (
            <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-md">
              <p className="text-gray-500 dark:text-gray-400">
                No materials added yet. Click one of the "Add Material" buttons to add materials to this receipt.
              </p>
            </div>
          )}

          {materials.length > 0 && (
            <div className="space-y-4">
              {materials.map((material, index) => (
                <div key={material.id} className="p-4 border rounded-md bg-gray-50 dark:bg-gray-800 relative">
                  <button
                    type="button"
                    onClick={() => removeMaterial(material.id)}
                    className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                    disabled={isSubmitting}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Material Category */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Material Category <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={material.category}
                        onChange={(e) => handleMaterialChange(material.id, 'category', e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        required
                        disabled={isSubmitting}
                      >
                        {MATERIAL_CATEGORIES.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    {/* Material Type - changes based on category */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        {material.category === 'metal' ? 'Metal Type' : 
                         material.category === 'stone' ? 'Stone Type' : 
                         material.category === 'diamond' ? 'Diamond Type' : 'Material Type'} <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={material.material_type_id}
                        onChange={(e) => handleMaterialChange(material.id, 'material_type_id', e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        required
                        disabled={isSubmitting}
                      >
                        <option value="">Select Type</option>
                        {material.category === 'metal' && metalTypes.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                        {(material.category === 'stone' || material.category === 'diamond') && stoneTypes.map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    {/* Weight - for all material types */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Weight {material.category === 'metal' ? '(g)' : '(ct)'} <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        value={material.weight}
                        onChange={(e) => handleMaterialChange(material.id, 'weight', parseFloat(e.target.value))}
                        step="0.001"
                        min="0"
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        required
                        disabled={isSubmitting}
                      />
                    </div>
                    
                    {/* Purity - only for metals */}
                    {material.category === 'metal' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Purity
                        </label>
                        <input
                          type="text"
                          value={material.purity || ''}
                          onChange={(e) => handleMaterialChange(material.id, 'purity', e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                          placeholder="e.g., 24K, 92.5%"
                          disabled={isSubmitting}
                        />
                      </div>
                    )}
                    
                    {/* Quantity - only for stones/diamonds */}
                    {(material.category === 'stone' || material.category === 'diamond') && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Quantity <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="number"
                          value={material.quantity || 0}
                          onChange={(e) => handleMaterialChange(material.id, 'quantity', parseInt(e.target.value))}
                          min="1"
                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                          required
                          disabled={isSubmitting}
                        />
                      </div>
                    )}
                    
                    {/* Size - only for stones/diamonds */}
                    {(material.category === 'stone' || material.category === 'diamond') && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Size (mm)
                        </label>
                        <input
                          type="number"
                          value={material.size_mm || 0}
                          onChange={(e) => handleMaterialChange(material.id, 'size_mm', parseFloat(e.target.value))}
                          step="0.01"
                          min="0"
                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                          disabled={isSubmitting}
                        />
                      </div>
                    )}
                    
                    {/* Shape - only for stones/diamonds */}
                    {(material.category === 'stone' || material.category === 'diamond') && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Shape
                        </label>
                        <select
                          value={material.shape_id || ''}
                          onChange={(e) => handleMaterialChange(material.id, 'shape_id', e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                          disabled={loadingStoneShapes || isSubmitting}
                        >
                          <option value="">Select Shape</option>
                          {stoneShapes.map(option => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    )}
                    
                    {/* Description - for all material types */}
                    <div className={material.category === 'metal' ? '' : 'md:col-span-2'}>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        value={material.description || ''}
                        onChange={(e) => handleMaterialChange(material.id, 'description', e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:text-white sm:text-sm"
                        placeholder="Additional details"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isSubmitting || materials.length === 0}
          >
            {isSubmitting ? 'Saving...' : initialData?.receipt_id ? 'Update' : 'Create'}
          </button>
        </div>
      </form>
    </div>
  );
};
