'use client';

import React from 'react';
import { MobileMenu } from './MobileMenu';
import { ThemeToggle } from './ThemeToggle';
import { UserMenu } from './UserMenu';

interface HeaderProps {
  isSidebarOpen: boolean;
  onSidebarToggle: () => void;
}

export function Header({ isSidebarOpen, onSidebarToggle }: HeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 lg:left-72 z-50 h-16 bg-[#1a1f2b] border-b border-[#2a2f3a] flex items-center justify-between">
      <div className="flex items-center gap-4 px-4">
        <div className="lg:hidden">
          <MobileMenu isOpen={isSidebarOpen} onToggle={onSidebarToggle} />
        </div>
        <h1 className="text-xl font-semibold text-white">
          Production Planning System
        </h1>
      </div>
      <div className="flex items-center gap-4 px-4">
        <ThemeToggle />
        <UserMenu />
      </div>
    </header>
  );
}
