/**
 * Material Status Page
 * Comprehensive view of all material operations and their current status
 * 
 * @module app/materials/status
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ListChecksIcon, 
  SearchIcon,
  FilterIcon,
  GemIcon,
  CircleIcon,
  ScaleIcon,
  ClockIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  UserIcon,
  PackageIcon
} from 'lucide-react';

interface MaterialTransaction {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  customer: string;
  worker: string;
  process: string;
  status: 'issued' | 'in_process' | 'completed' | 'overdue';
  issued_date: string;
  expected_return: string;
  actual_return?: string;
  loss_percentage?: number;
}

export default function MaterialStatusPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('all');

  const [transactions] = useState<MaterialTransaction[]>([
    {
      id: '1',
      type: 'stone',
      description: '50 pcs Diamond - Round - 2mm',
      customer: 'Customer A',
      worker: 'John Doe',
      process: 'Setting',
      status: 'completed',
      issued_date: '2025-06-26',
      expected_return: '2025-06-28',
      actual_return: '2025-06-28',
      loss_percentage: 1.2
    },
    {
      id: '2',
      type: 'metal',
      description: '25.5g Gold - 18K',
      customer: 'Customer B',
      worker: 'Jane Smith',
      process: 'Filing',
      status: 'overdue',
      issued_date: '2025-06-25',
      expected_return: '2025-06-27'
    },
    {
      id: '3',
      type: 'finding',
      description: 'Polki Assembly - 15.2g',
      customer: 'Customer A',
      worker: 'Mike Johnson',
      process: 'Setting',
      status: 'in_process',
      issued_date: '2025-06-27',
      expected_return: '2025-06-29'
    },
    {
      id: '4',
      type: 'stone',
      description: '30 pcs Ruby - Oval - 3mm',
      customer: 'Customer C',
      worker: 'Sarah Wilson',
      process: 'Pre-Polish',
      status: 'issued',
      issued_date: '2025-06-28',
      expected_return: '2025-06-30'
    },
    {
      id: '5',
      type: 'metal',
      description: '18.3g Gold - 22K',
      customer: 'Customer B',
      worker: 'Tom Brown',
      process: 'Polishing',
      status: 'completed',
      issued_date: '2025-06-26',
      expected_return: '2025-06-28',
      actual_return: '2025-06-28',
      loss_percentage: 3.5
    }
  ]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stone':
        return <GemIcon className="w-4 h-4" />;
      case 'finding':
        return <CircleIcon className="w-4 h-4" />;
      case 'metal':
        return <ScaleIcon className="w-4 h-4" />;
      default:
        return <PackageIcon className="w-4 h-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'issued':
        return <Badge variant="secondary">Issued</Badge>;
      case 'in_process':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Process</Badge>;
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'overdue':
        return <Badge variant="destructive">Overdue</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'issued':
        return <ClockIcon className="w-4 h-4" />;
      case 'in_process':
        return <ClockIcon className="w-4 h-4" />;
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'overdue':
        return <AlertTriangleIcon className="w-4 h-4" />;
      default:
        return <PackageIcon className="w-4 h-4" />;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'stone':
        return 'bg-blue-100 text-blue-800';
      case 'finding':
        return 'bg-purple-100 text-purple-800';
      case 'metal':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.worker.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;
    const matchesTab = activeTab === 'all' || transaction.status === activeTab;

    return matchesSearch && matchesStatus && matchesType && matchesTab;
  });

  const getStatusCounts = () => {
    return {
      all: transactions.length,
      issued: transactions.filter(t => t.status === 'issued').length,
      in_process: transactions.filter(t => t.status === 'in_process').length,
      completed: transactions.filter(t => t.status === 'completed').length,
      overdue: transactions.filter(t => t.status === 'overdue').length
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Material Status</h1>
        <p className="text-gray-600 dark:text-gray-400">
          Track all material operations and their current status
        </p>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by description, customer, or worker..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-40 h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Status</option>
                <option value="issued">Issued</option>
                <option value="in_process">In Process</option>
                <option value="completed">Completed</option>
                <option value="overdue">Overdue</option>
              </select>
              
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="w-40 h-10 px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">All Types</option>
                <option value="stone">Stones</option>
                <option value="finding">Findings</option>
                <option value="metal">Metals</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <ListChecksIcon className="w-4 h-4" />
            All ({statusCounts.all})
          </TabsTrigger>
          <TabsTrigger value="issued" className="flex items-center gap-2">
            <ClockIcon className="w-4 h-4" />
            Issued ({statusCounts.issued})
          </TabsTrigger>
          <TabsTrigger value="in_process" className="flex items-center gap-2">
            <ClockIcon className="w-4 h-4" />
            In Process ({statusCounts.in_process})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2">
            <CheckCircleIcon className="w-4 h-4" />
            Completed ({statusCounts.completed})
          </TabsTrigger>
          <TabsTrigger value="overdue" className="flex items-center gap-2">
            <AlertTriangleIcon className="w-4 h-4" />
            Overdue ({statusCounts.overdue})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {getStatusIcon(activeTab)}
                Material Transactions
              </CardTitle>
              <CardDescription>
                {filteredTransactions.length} transactions found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Worker</TableHead>
                    <TableHead>Process</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Issued Date</TableHead>
                    <TableHead>Expected Return</TableHead>
                    <TableHead>Loss %</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getTypeIcon(transaction.type)}
                          <Badge className={getTypeBadgeColor(transaction.type)}>
                            {transaction.type}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {transaction.description}
                      </TableCell>
                      <TableCell>{transaction.customer}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <UserIcon className="w-3 h-3" />
                          {transaction.worker}
                        </div>
                      </TableCell>
                      <TableCell>{transaction.process}</TableCell>
                      <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                      <TableCell>
                        {new Date(transaction.issued_date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {new Date(transaction.expected_return).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        {transaction.loss_percentage !== undefined ? (
                          <span className={transaction.loss_percentage > 3 ? 'text-red-600 font-semibold' : 'text-green-600'}>
                            {transaction.loss_percentage.toFixed(1)}%
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                          {transaction.status === 'issued' || transaction.status === 'in_process' ? (
                            <Button variant="outline" size="sm">
                              Receive
                            </Button>
                          ) : null}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredTransactions.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No transactions found matching your criteria
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
