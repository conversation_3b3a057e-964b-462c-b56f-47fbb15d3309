import { supabase } from '@/lib/db';
// Corrected import paths and replaced missing WorkerShift with any
import { Worker } from '@/types/domain/worker.types'; 
import { Process, ProcessStatus } from '@/types/process'; // Added ProcessStatus import
// import { WorkerShift } from '@/types/worker'; // Type not found, using any for now

class SchedulingService {
  async getProcesses(): Promise<Process[]> {
    const { data: processes, error } = await supabase
      .from('process_tracking')
      .select(`
        *,
        orders (
          order_id,
          item_type_id,
          expected_delivery_date,
          customer_id,
          order_category_id
        ),
        process_mast (
          process_id,
          description,
          standard_time,
          required_skills
        )
      `)
      .order('priority', { ascending: false });

    if (error) {
      throw new Error('Failed to fetch processes: ' + error.message);
    }

    // Get worker details in a separate query
    if (processes && processes.length > 0) {
      const workerIds = processes
        .filter(p => p.worker_id)
        .map(p => p.worker_id);

      if (workerIds.length > 0) {
        const { data: workers, error: workerError } = await supabase
          .from('workers')
          .select('*')
          .in('worker_id', workerIds);

        if (workerError) {
          console.error('Failed to fetch worker details:', workerError);
          return processes;
        }

        // Merge worker data
        return processes.map(process => ({
          ...process,
          worker: workers?.find(w => w.worker_id === process.worker_id)
        }));
      }
    }

    return processes || [];
  }

  async getAvailableWorkers(date: Date): Promise<Worker[]> {
    const { data: workers, error } = await supabase
      .from('worker_mast') // Corrected table name
      .select('*')
      .eq('is_active', true);

    if (error) {
      throw new Error('Failed to fetch workers: ' + error.message);
    }

    return workers || [];
  }

  async getProcessQueue(): Promise<Process[]> {
    const { data: processes, error } = await supabase
      .from('process_tracking')
      .select(`
        *,
        orders (
          order_id,
          item_type_id,
          expected_delivery_date
        ),
        process_mast (
          process_id,
          description,
          standard_time
        )
      `)
      .eq('status', 'pending')
      .order('priority', { ascending: false });

    if (error) {
      throw new Error('Failed to fetch process queue: ' + error.message);
    }

    return processes || [];
  }

  async scheduleProcess(processId: string, workerId: string, startTime: Date): Promise<void> {
    const { error } = await supabase
      // Potential Issue: Updating based on process_id, not tracking_id (PK). 
      // This assumes process_id is unique in the context where this is called, or updates multiple records intentionally.
      // Ideally, this should update using tracking_id.
      .from('process_tracking')
      .update({
        worker_id: workerId,
        planned_start_time: startTime.toISOString(), // Corrected column name
        status: 'SCHEDULED' // Use uppercase to match ProcessStatus type
      })
      .eq('process_id', processId); 

    if (error) {
      throw new Error('Failed to schedule process: ' + error.message);
    }
  }

  async getWorkerSchedule(workerId: string, date: Date): Promise<Process[]> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const { data: processes, error } = await supabase
      .from('process_tracking')
      .select(`
        *,
        orders (
          order_id,
          item_type_id
        ),
        process_mast (
          process_id,
          description,
          standard_time
        )
      `)
      .eq('worker_id', workerId)
      .gte('planned_start_time', startOfDay.toISOString()) // Corrected column name
      .lte('planned_start_time', endOfDay.toISOString()) // Corrected column name (assuming filtering by planned time)
      .order('planned_start_time', { ascending: true }); // Corrected column name

    if (error) {
      throw new Error('Failed to fetch worker schedule: ' + error.message);
    }

    return processes || [];
  }

  // TODO: Define or find WorkerShift type definition. 
  // TODO: This method needs review. It targets 'workers' table (should be worker_mast?) 
  //       and updates a 'shifts' column which doesn't exist in worker_mast schema. 
  //       It might be intended for 'worker_shifts' table or needs different logic.
  // async updateWorkerAvailability(workerId: string, shifts: any[]): Promise<void> { // Changed WorkerShift[] to any[]
  //   const { error } = await supabase
  //     .from('worker_mast') // Corrected table name, but schema mismatch remains
  //     .update({
  //       // shifts: shifts // 'shifts' column does not exist in worker_mast
  //     })
  //     .eq('worker_id', workerId);
  // 
  //   if (error) {
  //     throw new Error('Failed to update worker availability: ' + error.message);
  //   }
  // }

  async updateProcessStatus(processId: string, status: ProcessStatus, notes?: string): Promise<void> {
    // Potential Issue: Updating based on process_id, not tracking_id (PK). See scheduleProcess comment.
    const { error } = await supabase
      .from('process_tracking')
      .update({
        status: status,
        notes: notes, // Corrected column name (was remarks)
        ...(status === 'COMPLETED' ? { actual_end_time: new Date().toISOString() } : {}) // Corrected column name (was end_time)
      })
      .eq('process_id', processId);

    if (error) {
      throw new Error('Failed to update process status: ' + error.message);
    }
  }

  async getWorkerEfficiency(workerId: string, processId: string): Promise<number> {
    const { data: history, error } = await supabase
      .from('process_history')
      .select(`
        actual_duration,
        process_mast (
          standard_time
        ),
        worker_mast ( 
          efficiency_factor
        )
      `) // Corrected table name
      .eq('worker_id', workerId)
      .eq('process_id', processId)
      .order('completed_at', { ascending: false })
      .limit(10);

    if (error) {
      throw new Error('Failed to fetch worker efficiency: ' + error.message);
    }

    if (!history || history.length === 0) {
      return 0;
    }

    const efficiencies = history.map(entry => {
      // Explicitly handle potential array type from Supabase join
      const processMast = entry.process_mast && Array.isArray(entry.process_mast) ? entry.process_mast[0] : entry.process_mast;
      const workerData = entry.worker_mast && Array.isArray(entry.worker_mast) ? entry.worker_mast[0] : entry.worker_mast; // Corrected table name reference

      const standardTime = processMast?.standard_time || 0; 
      const efficiencyFactor = workerData?.efficiency_factor || 1; 
      const actualDuration = entry.actual_duration || 0;

      if (!standardTime || !actualDuration) return 0;

      const expectedDuration = standardTime / efficiencyFactor;
      return Math.min(100, Math.round((expectedDuration / actualDuration) * 100));
    });

    return Math.round(efficiencies.reduce((a, b) => a + b, 0) / efficiencies.length);
  }

  async getWorkerWorkload(workerId: string, date: Date): Promise<number> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const { data: processes, error } = await supabase
      .from('process_tracking')
      .select(`
        process_mast (
          standard_time
        )
      `) // Removed non-existent estimated_duration
      .eq('worker_id', workerId)
      .gte('planned_start_time', startOfDay.toISOString()) // Corrected column name
      .lte('planned_start_time', endOfDay.toISOString()); // Corrected column name (assuming filtering by planned time)

    if (error) {
      throw new Error('Failed to fetch worker workload: ' + error.message);
    }

    if (!processes) return 0;

    return processes.reduce((total, process) => {
      // Explicitly handle potential array type from Supabase join
      const processMast = process.process_mast && Array.isArray(process.process_mast) ? process.process_mast[0] : process.process_mast;
      const standardTime = processMast?.standard_time || 0; 
      // Calculate workload based on standard_time as estimated_duration is not available
      return total + standardTime; 
    }, 0);
  }
}

export const schedulingService = new SchedulingService();
