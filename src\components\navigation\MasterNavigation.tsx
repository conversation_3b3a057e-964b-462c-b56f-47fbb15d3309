'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

const masterLinks = [
    { href: '/masters/customers', label: 'Customers' },
    { href: '/masters/item-types', label: 'Item Types' },
    { href: '/masters/metal-colors', label: 'Metal Colors' },
    { href: '/masters/purities', label: 'Purities' },
    { href: '/masters/processes', label: 'Processes' },
    { href: '/masters/workers', label: 'Workers' },
    { href: '/masters/workers/skills', label: 'Worker Skills' },
    { href: '/masters/holidays', label: 'Holidays' },
    { href: '/masters/workshop/config', label: 'Workshop Config' },
];

export function MasterNavigation() {
    const pathname = usePathname();

    return (
        <nav className="bg-white shadow-sm mb-6">
            <div className="container mx-auto px-4">
                <div className="flex space-x-4 overflow-x-auto py-4">
                    {masterLinks.map((link) => {
                        const isActive = pathname === link.href;
                        return (
                            <Link
                                key={link.href}
                                href={link.href}
                                className={`
                                    whitespace-nowrap px-3 py-2 rounded-md text-sm font-medium
                                    ${isActive
                                        ? 'bg-blue-100 text-blue-700'
                                        : 'text-gray-600 hover:bg-gray-50'
                                    }
                                `}
                            >
                                {link.label}
                            </Link>
                        );
                    })}
                </div>
            </div>
        </nav>
    );
}
