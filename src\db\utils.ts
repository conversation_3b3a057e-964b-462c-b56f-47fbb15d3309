import { supabase } from '@/lib/db';

export async function withTransaction<T>(
  callback: () => Promise<T>
): Promise<T> {
  try {
    await supabase.rpc('begin_transaction');
    const result = await callback();
    await supabase.rpc('commit_transaction');
    return result;
  } catch (error) {
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

export function handleError(error: unknown): never {
  console.error('Database error:', error);
  throw error instanceof Error ? error : new Error(String(error));
}
