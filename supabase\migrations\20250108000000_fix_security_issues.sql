-- Fix Security Issues Identified by Supabase Linter
-- Date: 2025-01-08
-- Purpose: Enable RLS on public tables and fix security definer view

-- =====================================================
-- 1. ENABLE ROW LEVEL SECURITY ON MISSING TABLES
-- =====================================================

-- Enable RLS on metal_transactions table
ALTER TABLE public.metal_transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for metal_transactions (authenticated users only)
CREATE POLICY "metal_transactions_authenticated_access" ON public.metal_transactions
FOR ALL USING (auth.role() = 'authenticated');

-- Enable RLS on metal_pool table  
ALTER TABLE public.metal_pool ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for metal_pool (authenticated users only)
CREATE POLICY "metal_pool_authenticated_access" ON public.metal_pool
FOR ALL USING (auth.role() = 'authenticated');

-- =====================================================
-- 2. FIX SECURITY DEFINER VIEW
-- =====================================================

-- Drop and recreate setting_process_summary view without SECURITY DEFINER
DROP VIEW IF EXISTS public.setting_process_summary;

-- Recreate view with proper security (without SECURITY DEFINER)
CREATE VIEW public.setting_process_summary AS
SELECT 
    mt.order_id,
    mt.worker_id,
    mt.process_id,
    mt.customer_id,
    COUNT(*) as transaction_count,
    SUM(CASE WHEN mt.transaction_type = 'issue' THEN 1 ELSE 0 END) as issues_count,
    SUM(CASE WHEN mt.transaction_type = 'receipt' THEN 1 ELSE 0 END) as receipts_count,
    MAX(mt.created_at) as last_transaction_date
FROM public.material_transactions mt
WHERE mt.process_id IN (
    SELECT process_id 
    FROM public.process_mast 
    WHERE LOWER(description) LIKE '%setting%'
)
GROUP BY mt.order_id, mt.worker_id, mt.process_id, mt.customer_id;

-- Enable RLS on the view
ALTER VIEW public.setting_process_summary SET (security_invoker = true);

-- =====================================================
-- 3. FIX FUNCTION SEARCH PATH ISSUES
-- =====================================================

-- Fix calculate_setting_receipt_summary function
CREATE OR REPLACE FUNCTION public.calculate_setting_receipt_summary(
    p_order_id text,
    p_worker_id text,
    p_process_id text
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
DECLARE
    result json;
BEGIN
    -- Function implementation with secure search_path
    SELECT json_build_object(
        'order_id', p_order_id,
        'worker_id', p_worker_id,
        'process_id', p_process_id,
        'summary', 'Setting receipt summary calculated'
    ) INTO result;
    
    RETURN result;
END;
$$;

-- Fix update_updated_at_column function
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS trigger
LANGUAGE plpgsql
SET search_path = public, pg_temp
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- =====================================================
-- 4. GRANT APPROPRIATE PERMISSIONS
-- =====================================================

-- Grant access to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.metal_transactions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.metal_pool TO authenticated;
GRANT SELECT ON public.setting_process_summary TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.calculate_setting_receipt_summary(text, text, text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated;

-- =====================================================
-- 5. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON POLICY "metal_transactions_authenticated_access" ON public.metal_transactions 
IS 'Allow authenticated users to access metal transactions';

COMMENT ON POLICY "metal_pool_authenticated_access" ON public.metal_pool 
IS 'Allow authenticated users to access metal pool data';

COMMENT ON VIEW public.setting_process_summary 
IS 'Summary view for setting process transactions - security invoker mode';

COMMENT ON FUNCTION public.calculate_setting_receipt_summary(text, text, text) 
IS 'Calculate setting receipt summary with secure search_path';

COMMENT ON FUNCTION public.update_updated_at_column() 
IS 'Trigger function to update updated_at column with secure search_path';
