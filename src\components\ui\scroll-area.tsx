// Placeholder for shadcn/ui scroll-area component
// Add using `npx shadcn-ui@latest add scroll-area`

import React from 'react';

export const ScrollArea = ({ children, ...props }: React.HTMLAttributes<HTMLDivElement>) => {
  console.warn('ScrollArea component from shadcn/ui not properly installed. Using placeholder.');
  return <div {...props}>{children}</div>;
};

export const ScrollBar = (props: React.HTMLAttributes<HTMLDivElement>) => {
  return <div {...props} style={{ display: 'none' }} />;
};
