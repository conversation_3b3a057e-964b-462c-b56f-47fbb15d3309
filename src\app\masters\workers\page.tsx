'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/db';
import { toast } from 'react-hot-toast';
import WorkerForm from '@/components/masters/WorkerForm';
import { useWorkerStore } from '@/store/workerStore';
import { workerQueries } from '@/db/queries/workers';
import type { Tables } from '@/db/types';

import { Worker as DomainWorker } from '@/types/domain/worker.types';

export default function WorkersPage() {
    const { workers: storeWorkers, loading: storeLoading, error: storeError, fetchWorkers, deleteWorker } = useWorkerStore();

    const [isLoading, setIsLoading] = useState(true);
    const [showForm, setShowForm] = useState(false);
    const [selectedWorkerForForm, setSelectedWorkerForForm] = useState<Tables['worker_mast']['Row'] | null>(null);

    useEffect(() => {
        fetchWorkers().then(() => setIsLoading(false));
    }, [fetchWorkers]);

    const handleEdit = async (workerId: string) => {
        setIsLoading(true);
        setSelectedWorkerForForm(null); 
        try {
            const fullWorker = await workerQueries.getById(workerId);

            if (!fullWorker) {
                toast.error('Failed to fetch worker details.');
                return;
            }
            setSelectedWorkerForForm(fullWorker);
            setShowForm(true);
        } catch (err) {
            toast.error('An unexpected error occurred while fetching worker details.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    };

    const handleAddNew = () => {
        setSelectedWorkerForForm(null); 
        setShowForm(true);
    };

    const handleDelete = async (workerId: string) => {
        if (!confirm('Are you sure you want to delete this worker?')) return;

        try {
            await deleteWorker(workerId);
            toast.success('Worker deleted successfully');
        } catch (err) {
            const error = err as Error;
            if (error.name === 'ForeignKeyError' || error.message.includes('foreign key constraint')) {
                toast.error('Cannot delete worker as they have related records');
            } else if (error instanceof Error) {
                toast.error(error.message || 'Failed to delete worker');
            } else {
                toast.error('Failed to delete worker');
            }
            console.error('Error deleting worker:', error);
        }
    };

    const handleSubmit = async () => {
        try {
            await fetchWorkers();
            setShowForm(false);
            setSelectedWorkerForForm(null);
        } catch (error) {
            console.error('Error after form submission:', error);
        }
    };

    const handleCancel = () => {
        setShowForm(false);
        setSelectedWorkerForForm(null);
    };

    if (isLoading || storeLoading) {
        return (
            <div className="flex justify-center items-center min-h-[50vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
            </div>
        );
    }

  return (
    <div className="container mx-auto p-4 min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Workers</h1>
        <button
          onClick={handleAddNew}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                >
                    Add Worker
                </button>
            </div>

            {storeError ? (
                <div className="text-center py-6 text-red-600">
                    <p>Error loading workers: {storeError.message}</p>
                </div>
      ) : storeWorkers.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">No workers found. Click "Add Worker" to create one.</p>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                  {/* Removed Contact column as 'code' field doesn't exist in schema */}
                  {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th> */}
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Active</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {storeWorkers.map((worker) => (
                  <tr key={worker.worker_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{worker.name}</td>
                    {/* Removed Contact column display */}
                    {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{worker.code || '-'}</td> */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{worker.worker_type || '-'}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        worker.is_active 
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' 
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100'
                      }`}>
                        {worker.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleEdit(worker.worker_id)}
                        className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-4 focus:outline-none focus:underline"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(worker.worker_id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 focus:outline-none focus:underline"
                                            >
                                                Delete
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {showForm && (
                <WorkerForm
                    worker={selectedWorkerForForm}
                    onSubmit={handleSubmit}
                    onCancel={handleCancel}
                />
            )}
        </div>
    );
}
