/**
 * Dust Management Service
 * 
 * Handles dust collection, parcel creation, and refining batch operations
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';

type DustParcelsEnhanced = Database['public']['Tables']['dust_parcels_enhanced']['Row'];
type DustRefineBatches = Database['public']['Tables']['dust_refine_batches']['Row'];

export interface DustParcelRequest {
  transaction_id?: string;
  worker_id: string;
  process_id: string;
  customer_id: string;
  weight_grams: number;
  purity_estimate?: number;
  collection_notes?: string;
}

export interface DustRefineBatchRequest {
  batch_name: string;
  parcel_ids: string[];
  expected_recovery_rate?: number;
  refining_method: string;
  estimated_output_weight?: number;
  notes?: string;
}

export interface DustRefineBatch extends DustRefineBatches {
  parcels: DustParcelsEnhanced[];
}

/**
 * Create dust parcel from collected dust
 */
export async function createDustParcel(request: DustParcelRequest): Promise<DustParcelsEnhanced> {
  try {
    const dustParcelData = {
      transaction_id: request.transaction_id,
      worker_id: request.worker_id,
      process_id: request.process_id,
      customer_id: request.customer_id,
      weight_grams: request.weight_grams,
      purity_estimate: request.purity_estimate || 0.22, // Default 22kt for gold dust
      expected_recovery_rate: 0.8, // 80% recovery rate
      collection_date: new Date().toISOString(),
      status: 'collected',
      collection_notes: request.collection_notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('dust_parcels_enhanced')
      .insert([dustParcelData])
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating dust parcel:', error);
    throw error;
  }
}

/**
 * Create refining batch from multiple dust parcels
 */
export async function createRefineBatch(request: DustRefineBatchRequest): Promise<DustRefineBatch> {
  try {
    // 1. Validate all parcels exist and are available
    const { data: parcels, error: parcelsError } = await supabase
      .from('dust_parcels_enhanced')
      .select('*')
      .in('parcel_id', request.parcel_ids)
      .eq('status', 'collected');

    if (parcelsError) throw parcelsError;

    if (!parcels || parcels.length !== request.parcel_ids.length) {
      throw new Error('Some parcels are not available for batching');
    }

    // 2. Calculate batch totals
    const totalWeight = parcels.reduce((sum, parcel) => sum + parcel.weight_grams, 0);
    const avgPurity = parcels.reduce((sum, parcel) => sum + (parcel.purity_estimate || 0), 0) / parcels.length;
    const expectedOutput = totalWeight * (request.expected_recovery_rate || 0.8);

    // 3. Create refine batch
    const batchData = {
      batch_name: request.batch_name,
      total_input_weight_grams: totalWeight,
      average_purity_estimate: avgPurity,
      expected_recovery_rate: request.expected_recovery_rate || 0.8,
      expected_output_weight_grams: request.estimated_output_weight || expectedOutput,
      refining_method: request.refining_method,
      batch_date: new Date().toISOString(),
      status: 'pending',
      notes: request.notes,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: batch, error: batchError } = await supabase
      .from('dust_refine_batches')
      .insert([batchData])
      .select()
      .single();

    if (batchError) throw batchError;

    // 4. Update parcels to reference batch
    const { error: updateError } = await supabase
      .from('dust_parcels_enhanced')
      .update({
        refine_batch_id: batch.batch_id,
        status: 'batched',
        updated_at: new Date().toISOString()
      })
      .in('parcel_id', request.parcel_ids);

    if (updateError) throw updateError;

    return {
      ...batch,
      parcels
    };
  } catch (error) {
    console.error('Error creating refine batch:', error);
    throw error;
  }
}

/**
 * Process refining batch completion
 */
export async function completeRefineBatch(
  batchId: string,
  actualOutputWeight: number,
  outputPurity: number,
  refiningCost: number,
  completionNotes?: string
): Promise<DustRefineBatch> {
  try {
    const actualRecoveryRate = await calculateActualRecoveryRate(batchId, actualOutputWeight);

    const { data, error } = await supabase
      .from('dust_refine_batches')
      .update({
        actual_output_weight_grams: actualOutputWeight,
        output_purity: outputPurity,
        actual_recovery_rate: actualRecoveryRate,
        refining_cost: refiningCost,
        completion_date: new Date().toISOString(),
        status: 'completed',
        completion_notes: completionNotes,
        updated_at: new Date().toISOString()
      })
      .eq('batch_id', batchId)
      .select(`
        *,
        parcels:dust_parcels_enhanced(*)
      `)
      .single();

    if (error) throw error;

    // Update parcel statuses
    await supabase
      .from('dust_parcels_enhanced')
      .update({
        status: 'refined',
        updated_at: new Date().toISOString()
      })
      .eq('refine_batch_id', batchId);

    return data;
  } catch (error) {
    console.error('Error completing refine batch:', error);
    throw error;
  }
}

/**
 * Calculate actual recovery rate for a batch
 */
async function calculateActualRecoveryRate(batchId: string, actualOutputWeight: number): Promise<number> {
  try {
    const { data: batch, error } = await supabase
      .from('dust_refine_batches')
      .select('total_input_weight_grams')
      .eq('batch_id', batchId)
      .single();

    if (error) throw error;

    return (actualOutputWeight / batch.total_input_weight_grams) * 100;
  } catch (error) {
    console.error('Error calculating recovery rate:', error);
    return 0;
  }
}

/**
 * Get dust parcels by status
 */
export async function getDustParcelsByStatus(status: string) {
  try {
    const { data, error } = await supabase
      .from('dust_parcels_enhanced')
      .select(`
        *,
        worker:workers_mast(worker_name),
        process:process_mast(process_name),
        customer:customers_mast(customer_name)
      `)
      .eq('status', status)
      .order('collection_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching dust parcels:', error);
    throw error;
  }
}

/**
 * Get dust parcels by customer
 */
export async function getDustParcelsByCustomer(customerId: string) {
  try {
    const { data, error } = await supabase
      .from('dust_parcels_enhanced')
      .select(`
        *,
        worker:workers_mast(worker_name),
        process:process_mast(process_name),
        refine_batch:dust_refine_batches(batch_name, status, completion_date)
      `)
      .eq('customer_id', customerId)
      .order('collection_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching customer dust parcels:', error);
    throw error;
  }
}

/**
 * Get available parcels for batching
 */
export async function getAvailableParcelsForBatching() {
  try {
    const { data, error } = await supabase
      .from('dust_parcels_enhanced')
      .select(`
        *,
        worker:workers_mast(worker_name),
        process:process_mast(process_name),
        customer:customers_mast(customer_name)
      `)
      .eq('status', 'collected')
      .order('collection_date', { ascending: true });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching available parcels:', error);
    throw error;
  }
}

/**
 * Get refining batches by status
 */
export async function getRefineBatchesByStatus(status: string) {
  try {
    const { data, error } = await supabase
      .from('dust_refine_batches')
      .select(`
        *,
        parcels:dust_parcels_enhanced(
          parcel_id,
          weight_grams,
          purity_estimate,
          worker:workers_mast(worker_name),
          customer:customers_mast(customer_name)
        )
      `)
      .eq('status', status)
      .order('batch_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching refine batches:', error);
    throw error;
  }
}

/**
 * Get dust collection summary by worker
 */
export async function getDustCollectionSummaryByWorker(workerId: string, dateFrom?: string, dateTo?: string) {
  try {
    let query = supabase
      .from('dust_parcels_enhanced')
      .select(`
        parcel_id,
        weight_grams,
        purity_estimate,
        collection_date,
        process:process_mast(process_name),
        status
      `)
      .eq('worker_id', workerId);

    if (dateFrom) query = query.gte('collection_date', dateFrom);
    if (dateTo) query = query.lte('collection_date', dateTo);

    const { data, error } = await query.order('collection_date', { ascending: false });

    if (error) throw error;

    // Calculate summary statistics
    const totalWeight = data?.reduce((sum, parcel) => sum + parcel.weight_grams, 0) || 0;
    const avgPurity = data?.length ? data.reduce((sum, parcel) => sum + (parcel.purity_estimate || 0), 0) / data.length : 0;

    return {
      parcels: data,
      summary: {
        total_parcels: data?.length || 0,
        total_weight_grams: totalWeight,
        average_purity: avgPurity,
        estimated_refined_weight: totalWeight * 0.8 // 80% recovery
      }
    };
  } catch (error) {
    console.error('Error fetching worker dust collection summary:', error);
    throw error;
  }
}

/**
 * Get dust recovery report
 */
export async function getDustRecoveryReport(dateFrom?: string, dateTo?: string) {
  try {
    let query = supabase
      .from('dust_refine_batches')
      .select(`
        batch_id,
        batch_name,
        total_input_weight_grams,
        actual_output_weight_grams,
        actual_recovery_rate,
        refining_cost,
        completion_date,
        status
      `)
      .eq('status', 'completed');

    if (dateFrom) query = query.gte('completion_date', dateFrom);
    if (dateTo) query = query.lte('completion_date', dateTo);

    const { data, error } = await query.order('completion_date', { ascending: false });

    if (error) throw error;

    // Calculate totals
    const totalInput = data?.reduce((sum, batch) => sum + batch.total_input_weight_grams, 0) || 0;
    const totalOutput = data?.reduce((sum, batch) => sum + (batch.actual_output_weight_grams || 0), 0) || 0;
    const totalCost = data?.reduce((sum, batch) => sum + (batch.refining_cost || 0), 0) || 0;
    const avgRecoveryRate = data?.length ? data.reduce((sum, batch) => sum + (batch.actual_recovery_rate || 0), 0) / data.length : 0;

    return {
      batches: data,
      summary: {
        total_batches: data?.length || 0,
        total_input_weight_grams: totalInput,
        total_output_weight_grams: totalOutput,
        average_recovery_rate: avgRecoveryRate,
        total_refining_cost: totalCost,
        net_recovery_weight: totalOutput
      }
    };
  } catch (error) {
    console.error('Error fetching dust recovery report:', error);
    throw error;
  }
}

/**
 * Get dust parcels ready for refining (alias for getAvailableParcelsForBatching)
 */
export async function getDustParcelsForRefining() {
  return getAvailableParcelsForBatching();
}

/**
 * Create refining batch (alias for createRefineBatch)
 */
export async function createRefiningBatch(parcelIds: string[], batchData: {
  batch_name: string;
  expected_recovery_rate: number;
  refinery_name?: string;
  notes?: string;
}) {
  return createRefineBatch({
    batch_name: batchData.batch_name,
    parcel_ids: parcelIds,
    expected_recovery_rate: batchData.expected_recovery_rate,
    refining_method: batchData.refinery_name || 'Standard',
    notes: batchData.notes
  });
}

/**
 * Complete refining batch (alias for completeRefineBatch)
 */
export async function completeRefiningBatch(
  batchId: string,
  actualOutputWeight: number,
  outputPurity: number,
  refiningCost: number,
  completionNotes?: string
) {
  return completeRefineBatch(batchId, actualOutputWeight, outputPurity, refiningCost, completionNotes);
}
