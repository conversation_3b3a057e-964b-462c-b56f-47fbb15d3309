/**
 * Interface for third party customer data
 */
export interface ThirdPartyCustomer {
  party_cust_id?: string;
  description: string; // Up to 100 characters
  created_at?: Date;
  updated_at?: Date;
}

/**
 * Form configuration for third party customer fields
 */
export const thirdPartyCustomerFields = [
  {
    name: 'description',
    label: 'Customer Name',
    type: 'text',
    required: true,
    maxLength: 100,
    placeholder: 'Enter customer name',
  },
] as const;
