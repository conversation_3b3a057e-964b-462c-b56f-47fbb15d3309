/**
 * @module components/material-receipt/MetalReceiptForm
 * @description Metal Receipt Form Component - Receive metal back from workers with loss tracking
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertTriangleIcon, CheckCircleIcon, ScaleIcon } from 'lucide-react';
import { receiveMaterial, getIssuedMaterials } from '@/services/materialTransactionService';

const metalReceiptSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  received_weight_grams: z.number().min(0, 'Received weight must be positive'),
  dust_collected_grams: z.number().min(0, 'Dust weight must be positive').optional(),
  notes: z.string().optional()
});

interface MetalReceiptFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  received_weight_grams: number;
  dust_collected_grams?: number;
  notes?: string;
}

interface IssuedMetalItem {
  transaction_id: string;
  metal_type_id: string;
  karat_id: string;
  weight_grams_issued: number;
  issue_date: string;
  expected_loss_percentage: number;
  metal_type?: {
    metal_name: string;
  };
  karat?: {
    karat_name: string;
    purity_percentage: number;
  };
  worker?: {
    name: string;
  };
  process?: {
    name: string;
  };
}

interface MetalReceiptItem extends IssuedMetalItem {
  received_weight_grams: number;
  dust_collected_grams: number;
  actual_loss_percentage: number;
  expected_loss_grams: number;
  actual_loss_grams: number;
  loss_variance: number;
  is_loss_acceptable: boolean;
}

export function MetalReceiptForm() {
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [issuedMetals, setIssuedMetals] = useState<IssuedMetalItem[]>([]);
  const [receiptItems, setReceiptItems] = useState<MetalReceiptItem[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [selectedOrderId, setSelectedOrderId] = useState('');
  const [selectedWorkerId, setSelectedWorkerId] = useState('');
  const [selectedProcessId, setSelectedProcessId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<MetalReceiptFormData>({
    resolver: zodResolver(metalReceiptSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      received_weight_grams: 0,
      dust_collected_grams: 0,
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load issued metals when all required fields are selected
  useEffect(() => {
    if (selectedCustomerId && selectedOrderId && selectedWorkerId && selectedProcessId) {
      loadIssuedMetals();
    }
  }, [selectedCustomerId, selectedOrderId, selectedWorkerId, selectedProcessId]);

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel
      const [customersRes, workersRes, processesRes] = await Promise.all([
        fetch('/api/masters/customers'),
        fetch('/api/masters/workers'),
        fetch('/api/masters/processes')
      ]);

      const [customersData, workersData, processesData] = await Promise.all([
        customersRes.json(),
        workersRes.json(),
        processesRes.json()
      ]);

      setCustomers(customersData);
      setWorkers(workersData);
      setProcesses(processesData);
    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadOrdersForCustomer = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error('Failed to load orders');
    }
  };

  const loadIssuedMetals = async () => {
    try {
      setIsLoading(true);
      
      const metals = await getIssuedMaterials({
        customer_id: selectedCustomerId,
        order_id: selectedOrderId,
        worker_id: selectedWorkerId,
        process_id: selectedProcessId,
        material_type: 'metal'
      });

      setIssuedMetals(metals);
      
      // Initialize receipt items
      const initialReceiptItems = metals.map(metal => ({
        ...metal,
        received_weight_grams: 0,
        dust_collected_grams: 0,
        actual_loss_percentage: 0,
        expected_loss_grams: (metal.weight_grams_issued * metal.expected_loss_percentage) / 100,
        actual_loss_grams: 0,
        loss_variance: 0,
        is_loss_acceptable: true
      }));
      
      setReceiptItems(initialReceiptItems);
    } catch (error) {
      console.error('Error loading issued metals:', error);
      toast.error('Failed to load issued metals');
    } finally {
      setIsLoading(false);
    }
  };

  const updateReceiptItem = (transactionId: string, field: keyof MetalReceiptItem, value: number) => {
    setReceiptItems(prev => prev.map(item => {
      if (item.transaction_id === transactionId) {
        const updated = { ...item, [field]: value };
        
        // Recalculate loss metrics
        if (field === 'received_weight_grams' || field === 'dust_collected_grams') {
          const totalReceived = updated.received_weight_grams + (updated.dust_collected_grams || 0);
          updated.actual_loss_grams = updated.weight_grams_issued - totalReceived;
          updated.actual_loss_percentage = (updated.actual_loss_grams / updated.weight_grams_issued) * 100;
          updated.loss_variance = updated.actual_loss_percentage - updated.expected_loss_percentage;
          updated.is_loss_acceptable = Math.abs(updated.loss_variance) <= 0.5; // 0.5% tolerance
        }
        
        return updated;
      }
      return item;
    }));
  };

  const onSubmit = async (data: MetalReceiptFormData) => {
    if (receiptItems.length === 0) {
      toast.error('No metals to receive');
      return;
    }

    const hasInvalidItems = receiptItems.some(item => 
      item.received_weight_grams <= 0 || 
      item.received_weight_grams > item.weight_grams_issued
    );

    if (hasInvalidItems) {
      toast.error('Please enter valid received weights for all items');
      return;
    }

    setIsSubmitting(true);
    try {
      // Process each receipt item
      for (const item of receiptItems) {
        await receiveMaterial({
          transaction_id: item.transaction_id,
          received_weight_grams: item.received_weight_grams,
          dust_collected_grams: item.dust_collected_grams || 0,
          loss_percentage: item.actual_loss_percentage,
          received_by: 'current_user', // TODO: Get from auth context
          notes: data.notes
        });
      }

      toast.success('Metal received successfully');
      
      // Reset form
      form.reset();
      setSelectedCustomerId('');
      setSelectedOrderId('');
      setSelectedWorkerId('');
      setSelectedProcessId('');
      setIssuedMetals([]);
      setReceiptItems([]);
      
    } catch (error) {
      console.error('Error receiving metal:', error);
      toast.error('Failed to receive metal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTotalIssuedWeight = () => {
    return receiptItems.reduce((sum, item) => sum + item.weight_grams_issued, 0);
  };

  const getTotalReceivedWeight = () => {
    return receiptItems.reduce((sum, item) => sum + item.received_weight_grams, 0);
  };

  const getTotalDustCollected = () => {
    return receiptItems.reduce((sum, item) => sum + (item.dust_collected_grams || 0), 0);
  };

  const getTotalLoss = () => {
    return getTotalIssuedWeight() - getTotalReceivedWeight() - getTotalDustCollected();
  };

  const getOverallLossPercentage = () => {
    const totalIssued = getTotalIssuedWeight();
    return totalIssued > 0 ? (getTotalLoss() / totalIssued) * 100 : 0;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ScaleIcon className="w-5 h-5" />
            Receive Metal from Worker
          </CardTitle>
          <CardDescription>
            Record metal received back from worker after process completion
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Selection Fields */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="customer_id">Customer</Label>
                <Select
                  {...form.register('customer_id')}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSelectedCustomerId(value);
                    form.setValue('customer_id', value);
                    loadOrdersForCustomer(value);
                  }}
                  options={[
                    { value: '', label: 'Select customer' },
                    ...customers.map((customer: any) => ({
                      value: customer.customer_id,
                      label: customer.description
                    }))
                  ]}
                />
              </div>

              <div>
                <Label htmlFor="order_id">Order</Label>
                <Select
                  {...form.register('order_id')}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSelectedOrderId(value);
                    form.setValue('order_id', value);
                  }}
                  options={[
                    { value: '', label: 'Select order' },
                    ...orders.map((order: any) => ({
                      value: order.order_id,
                      label: order.order_reference_no
                    }))
                  ]}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="worker_id">Worker</Label>
                <Select
                  {...form.register('worker_id')}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSelectedWorkerId(value);
                    form.setValue('worker_id', value);
                  }}
                  options={[
                    { value: '', label: 'Select worker' },
                    ...workers.map((worker: any) => ({
                      value: worker.worker_id,
                      label: worker.name
                    }))
                  ]}
                />
              </div>

              <div>
                <Label htmlFor="process_id">Process</Label>
                <Select
                  {...form.register('process_id')}
                  onChange={(e) => {
                    const value = e.target.value;
                    setSelectedProcessId(value);
                    form.setValue('process_id', value);
                  }}
                  options={[
                    { value: '', label: 'Select process' },
                    ...processes.map((process: any) => ({
                      value: process.process_id,
                      label: process.name
                    }))
                  ]}
                />
              </div>
            </div>

            {/* Notes */}
            <div>
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Additional notes for this receipt"
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Issued Metals for Receipt */}
      {receiptItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Metal Receipt Details</CardTitle>
            <CardDescription>
              Enter received weights and dust collection for each metal item
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Metal Type</TableHead>
                    <TableHead>Karat</TableHead>
                    <TableHead>Issued (g)</TableHead>
                    <TableHead>Received (g)</TableHead>
                    <TableHead>Dust (g)</TableHead>
                    <TableHead>Loss %</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {receiptItems.map((item) => (
                    <TableRow key={item.transaction_id}>
                      <TableCell>
                        <Badge variant="secondary">
                          {item.metal_type?.metal_name || 'Unknown'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {item.karat?.karat_name || 'Unknown'} 
                          ({item.karat?.purity_percentage}%)
                        </Badge>
                      </TableCell>
                      <TableCell className="font-medium">
                        {item.weight_grams_issued.toFixed(3)}g
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          max={item.weight_grams_issued}
                          value={item.received_weight_grams || ''}
                          onChange={(e) => updateReceiptItem(
                            item.transaction_id, 
                            'received_weight_grams', 
                            parseFloat(e.target.value) || 0
                          )}
                          className="w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <Input
                          type="number"
                          step="0.001"
                          min="0"
                          value={item.dust_collected_grams || ''}
                          onChange={(e) => updateReceiptItem(
                            item.transaction_id, 
                            'dust_collected_grams', 
                            parseFloat(e.target.value) || 0
                          )}
                          className="w-24"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            Actual: {item.actual_loss_percentage.toFixed(2)}%
                          </div>
                          <div className="text-xs text-gray-500">
                            Expected: {item.expected_loss_percentage.toFixed(2)}%
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {item.is_loss_acceptable ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <CheckCircleIcon className="w-3 h-3 mr-1" />
                            OK
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            <AlertTriangleIcon className="w-3 h-3 mr-1" />
                            High Loss
                          </Badge>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Summary */}
              <div className="grid grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="text-center">
                  <div className="text-sm text-gray-500">Total Issued</div>
                  <div className="font-semibold">{getTotalIssuedWeight().toFixed(3)}g</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-500">Total Received</div>
                  <div className="font-semibold">{getTotalReceivedWeight().toFixed(3)}g</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-500">Total Dust</div>
                  <div className="font-semibold">{getTotalDustCollected().toFixed(3)}g</div>
                </div>
                <div className="text-center">
                  <div className="text-sm text-gray-500">Overall Loss</div>
                  <div className={`font-semibold ${getOverallLossPercentage() > 3 ? 'text-red-600' : 'text-green-600'}`}>
                    {getOverallLossPercentage().toFixed(2)}%
                  </div>
                </div>
              </div>

              {/* Loss Alert */}
              {getOverallLossPercentage() > 3 && (
                <Alert>
                  <AlertTriangleIcon className="h-4 w-4" />
                  <AlertDescription>
                    High loss percentage detected. Please verify the received weights and consider investigating the cause.
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setReceiptItems([]);
                    setIssuedMetals([]);
                    form.reset();
                  }}
                >
                  Cancel
                </Button>
                <Button
                  onClick={form.handleSubmit(onSubmit)}
                  disabled={isSubmitting || receiptItems.length === 0}
                >
                  {isSubmitting ? 'Processing...' : 'Complete Receipt'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
