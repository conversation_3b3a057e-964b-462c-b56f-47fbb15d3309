'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { supabase } from '@/lib/db';
import { workerQueries } from '@/db/queries/workers';
import type { Tables } from '@/db/types';

type Worker = Tables['worker_mast']['Row'];

interface Process {
    process_id: string;
    name: string;
    description?: string;
}

interface WorkerFormProps {
    worker?: Worker | null;
    onSubmit: (data: Worker) => void;
    onCancel: () => void;
}

interface WorkerFormFields {
    name: string;
    // contact_number: string; // Removed - Not in schema
    // address?: string | null; // Removed - Not in schema
    is_active: boolean;
    is_vendor: boolean;
    shift_start?: string | null;
    shift_end?: string | null;
    worker_type?: string | null;
    available_from?: string | null;
    available_to?: string | null;
    efficiency_factor: number;
    skill_ids: string[];
    skill_levels: Record<string, number>;
}

export default function WorkerForm({ worker, onSubmit, onCancel }: WorkerFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);

    // State for UI rendering of skills, synced with form state via useEffect/watch
    const [processes, setProcesses] = useState<Process[]>([]);
    const [selectedSkills, setSelectedSkills] = useState<string[]>(worker?.skills ? Object.keys(worker.skills) : []);
    const [skillLevels, setSkillLevels] = useState<Record<string, number>>(() => {
        const initialLevels: Record<string, number> = {};
        if (worker?.skills && typeof worker.skills === 'object') {
            const skills = worker.skills as Record<string, { level?: number }>;
            Object.keys(skills).forEach(id => {
                initialLevels[id] = (typeof skills[id]?.level === 'number') ? skills[id].level : 1;
            });
        }
        return initialLevels;
    });

    // !! IMPORTANT: Call useForm hook BEFORE useEffect hooks that use its methods !!
    const {
        register,
        handleSubmit,
        formState: { errors },
        setValue,
        watch,
        reset,
        getValues,
    } = useForm<WorkerFormFields>({
        defaultValues: {
            name: worker?.name || '',
            is_active: worker?.is_active ?? true,
            is_vendor: worker?.is_vendor ?? false,
            shift_start: worker?.shift_start || '',
            shift_end: worker?.shift_end || '',
            worker_type: worker?.worker_type || 'REGULAR',
            available_from: worker?.available_from ? worker.available_from.split('T')[0] : '',
            available_to: worker?.available_to ? worker.available_to.split('T')[0] : '',
            efficiency_factor: worker?.efficiency_factor || 1.0,
            skill_ids: [],
            skill_levels: {},
        },
    });

    useEffect(() => {
        async function fetchProcesses() {
            const { data, error } = await supabase
                .from('process_mast')
                .select('process_id, name, description')
                .order('name');

            if (error) {
                console.error('Error fetching processes:', error);
                toast.error('Failed to load processes');
                return;
            }

            setProcesses(data || []);
        }

        fetchProcesses();
    }, [worker, setValue]); // Add setValue to dependency array

    // Helper function to toggle skills (add/remove)
    // Must be defined inside the component to access state setters and setValue
    const toggleSkill = (processId: string) => {
        const currentSkillIds = getValues('skill_ids') || [];
        const currentSkillLevels = getValues('skill_levels') || {};

        let newSkills: string[];
        let newLevels: Record<string, number>;

        if (currentSkillIds.includes(processId)) {
            // Remove skill
            newSkills = currentSkillIds.filter(id => id !== processId);
            newLevels = { ...currentSkillLevels };
            delete newLevels[processId];
        } else {
            // Add skill with default level 1
            newSkills = [...currentSkillIds, processId];
            newLevels = { ...currentSkillLevels, [processId]: 1 };
        }
        // Update react-hook-form state
        setValue('skill_ids', newSkills, { shouldDirty: true });
        setValue('skill_levels', newLevels, { shouldDirty: true });

        // Update local state for immediate UI re-render
        setSelectedSkills(newSkills);
        setSkillLevels(newLevels);
    };

    // Helper function to change skill level
    // Must be defined inside the component to access state setters and setValue
    const changeSkillLevel = (processId: string, level: number) => {
        const currentSkillLevels = getValues('skill_levels') || {};
        const newLevels = { ...currentSkillLevels, [processId]: level };

        // Update react-hook-form state
        setValue('skill_levels', newLevels, { shouldDirty: true });

        // Update local state for immediate UI re-render
        setSkillLevels(newLevels);
    };

    // Watch form state for skills to keep local state in sync if needed (optional)
    // This might be redundant if toggleSkill/changeSkillLevel manage both reliably
    const watchedSkillIds = watch('skill_ids');
    useEffect(() => {
        if (watchedSkillIds) {
            setSelectedSkills(watchedSkillIds);
            // Potentially update skillLevels too if needed, but might cause loops
        }
    }, [watchedSkillIds]);

    const onSubmitForm = async (data: WorkerFormFields) => {
        setIsSubmitting(true);
        try {
            // Prepare skills data in the format expected by query functions
            // IMPORTANT: Must match Skill interface defined in workers.ts, which uses `skill_level`
            const skillsPayload = data.skill_ids.map(processId => ({
                process_id: processId,
                skill_level: data.skill_levels[processId] || 1, // Changed from 'level' to 'skill_level'
                assigned_date: new Date().toISOString(), // Add required date
            }));

            // Base payload common to insert and update
            const basePayload = {
                name: data.name,
                is_active: data.is_active,
                is_vendor: data.is_vendor,
                // Ensure efficiency_factor is number or null, handle potential NaN from form
                efficiency_factor: isNaN(Number(data.efficiency_factor)) ? null : Number(data.efficiency_factor),
                skills: skillsPayload,
            };

            let savedWorker: Worker;
            if (worker?.worker_id) { // This is an UPDATE
                const updatePayload = {
                    ...basePayload,
                    // Update type requires string | undefined (not null)
                    shift_start: data.shift_start || undefined,
                    shift_end: data.shift_end || undefined,
                    worker_type: data.worker_type || undefined, // Let DB keep existing value if form is empty
                    available_from: data.available_from ? new Date(data.available_from).toISOString() : undefined,
                    available_to: data.available_to ? new Date(data.available_to).toISOString() : undefined,
                };
                savedWorker = await workerQueries.updateWorker(worker.worker_id, updatePayload);

            } else { // This is an INSERT
                // Ensure required fields have valid defaults if form allows them to be empty/null
                const defaultIsoDate = new Date(0).toISOString(); // Placeholder default date for required fields
                const defaultTime = '00:00:00'; // Placeholder default time for required fields

                const insertPayload = {
                    ...basePayload,
                    // Insert type requires non-null strings for these fields
                    shift_start: data.shift_start || defaultTime,
                    shift_end: data.shift_end || defaultTime,
                    worker_type: data.worker_type || 'REGULAR', // Default required worker_type
                    available_from: data.available_from ? new Date(data.available_from).toISOString() : defaultIsoDate,
                    available_to: data.available_to ? new Date(data.available_to).toISOString() : defaultIsoDate,
                };
                // Type assertion might be needed if TS cannot infer insertPayload perfectly matches
                savedWorker = await workerQueries.createWorker(insertPayload as any); // Use 'as any' temporarily if issues persist, refine later
            }

            onSubmit(savedWorker);
            onCancel(); // Close form/modal on success
        } catch (error) {
            console.error('Error saving worker in form:', error);
            const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
            toast.error(`Failed to save worker: ${errorMessage}`);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 overflow-y-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-2xl my-8">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                    {worker ? 'Edit Worker' : 'New Worker'}
                </h2>

                <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
                    {/* Basic Information Section */}
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-5">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Worker Name <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    {...register('name', {
                                        required: 'Worker name is required',
                                        maxLength: {
                                            value: 100,
                                            message: 'Name cannot exceed 100 characters'
                                        }
                                    })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                                {errors.name && (
                                    <p className="text-red-500 dark:text-red-400 text-sm">{errors.name.message}</p>
                                )}
                            </div>

                            {/* Contact Number field removed */}

                            <div className="space-y-2">
                                <label htmlFor="worker_type" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Worker Type
                                </label>
                                <select
                                    id="worker_type"
                                    {...register('worker_type')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                >
                                    <option value="REGULAR">Regular</option>
                                    <option value="TEMPORARY">Temporary</option>
                                    <option value="CONTRACT">Contract</option>
                                    <option value="APPRENTICE">Apprentice</option>
                                </select>
                            </div>
                        </div>

                        {/* Address field removed */}
                    </div>

                    {/* Work Schedule Section */}
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-5">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Work Schedule</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label htmlFor="shift_start" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Shift Start Time
                                </label>
                                <input
                                    type="time"
                                    id="shift_start"
                                    {...register('shift_start')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>

                            <div className="space-y-2">
                                <label htmlFor="shift_end" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Shift End Time
                                </label>
                                <input
                                    type="time"
                                    id="shift_end"
                                    {...register('shift_end')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>

                            <div className="space-y-2">
                                <label htmlFor="available_from" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Available From
                                </label>
                                <input
                                    type="date"
                                    id="available_from"
                                    {...register('available_from')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>

                            <div className="space-y-2">
                                <label htmlFor="available_to" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Available To
                                </label>
                                <input
                                    type="date"
                                    id="available_to"
                                    {...register('available_to')}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Performance & Status Section */}
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-5">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance & Status</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <label htmlFor="efficiency_factor" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                                    Efficiency Factor <span className="text-red-500">*</span>
                                </label>
                                <input
                                    type="number"
                                    id="efficiency_factor"
                                    step="0.1"
                                    min="0.1"
                                    max="2.0"
                                    {...register('efficiency_factor', {
                                        required: 'Efficiency factor is required',
                                        min: {
                                            value: 0.1,
                                            message: 'Minimum efficiency is 0.1'
                                        },
                                        max: {
                                            value: 2.0,
                                            message: 'Maximum efficiency is 2.0'
                                        }
                                    })}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                                />
                                {errors.efficiency_factor && (
                                    <p className="text-red-500 dark:text-red-400 text-sm">{errors.efficiency_factor.message}</p>
                                )}
                                <p className="text-xs text-gray-500 dark:text-gray-400">1.0 represents standard efficiency. Higher values indicate greater productivity.</p>
                            </div>

                            <div className="space-y-4 flex flex-col justify-center">
                                <div className="flex items-center space-x-2">
                                    <input
                                     type="checkbox"
                                     id="is_active"
                                     {...register('is_active')}
                                     className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                 />
                                 <label htmlFor="is_active" className="text-sm font-medium text-gray-700 dark:text-gray-200">
                                     Active Worker
                                    </label>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <input
                                     type="checkbox"
                                     id="is_vendor"
                                     {...register('is_vendor')}
                                     className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                 />
                                 <label htmlFor="is_vendor" className="text-sm font-medium text-gray-700 dark:text-gray-200">
                                     External Vendor
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Skills Section */}
                    <div className="pb-5">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Worker Skills</h3>

                        <div className="border dark:border-gray-700 rounded-md p-4">
                            <div className="grid grid-cols-3 gap-2">
                                {processes.map(process => (
                                    <div key={process.process_id} className="flex flex-col border dark:border-gray-700 p-3 rounded-md">
                                        <div className="flex items-center mb-2">
                                            <input
                                                type="checkbox"
                                                id={`skill-${process.process_id}`}
                                                checked={selectedSkills.includes(process.process_id)}
                                                onChange={() => toggleSkill(process.process_id)}
                                                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                            />
                                            <label htmlFor={`skill-${process.process_id}`} className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-200 cursor-pointer">
                                                {process.name}
                                            </label>
                                        </div>

                                        {selectedSkills.includes(process.process_id) && (
                                            <div>
                                                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                                                    Skill Level (1-5)
                                                </label>
                                                <select
                                                    value={skillLevels[process.process_id] || 1}
                                                    onChange={(e) => changeSkillLevel(process.process_id, parseInt(e.target.value))}
                                                    className="w-full text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                                >
                                                    <option value="1">1 - Basic</option>
                                                    <option value="2">2 - Intermediate</option>
                                                    <option value="3">3 - Advanced</option>
                                                    <option value="4">4 - Expert</option>
                                                    <option value="5">5 - Master</option>
                                                </select>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                            {processes.length === 0 && (
                                <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">No processes available. Add processes first.</p>
                            )}
                        </div>
                    </div>

                    <div className="flex justify-end space-x-2 pt-4">
                        <button
                            type="button"
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 border border-transparent rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {isSubmitting ? 'Saving...' : worker ? 'Update' : 'Create'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
