/**
 * @module components/dashboard/MaterialFlowDashboard
 * @description Material Flow Dashboard - Real-time overview of all material activities
 * 
 * UX Features:
 * - Real-time updates with live data
 * - Exception highlighting for immediate attention
 * - Quick actions for common management tasks
 * - Interactive charts and trend analysis
 * - Mobile-responsive design for floor management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  ActivityIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  PackageIcon,
  ScaleIcon,
  SparklesIcon,
  RefreshCwIcon,
  FilterIcon,
  DownloadIcon
} from 'lucide-react';

interface Transaction {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  customer_name: string;
  worker_name: string;
  process_name: string;
  issued_weight: number;
  issued_date: string;
  expected_return: string;
  status: 'active' | 'overdue' | 'completed';
  priority: 'low' | 'medium' | 'high';
}

interface LossAlert {
  id: string;
  transaction_id: string;
  worker_name: string;
  process_name: string;
  material_type: string;
  loss_percentage: number;
  severity: 'warning' | 'critical';
  timestamp: string;
}

interface ProductivityMetric {
  period: string;
  materials_issued: number;
  materials_received: number;
  average_loss: number;
  dust_collected: number;
  efficiency_score: number;
}

interface DashboardData {
  activeTransactions: Transaction[];
  overdueItems: Transaction[];
  lossAlerts: LossAlert[];
  productivityMetrics: ProductivityMetric[];
  dustBatches: any[];
  summary: {
    totalActive: number;
    totalOverdue: number;
    totalAlerts: number;
    averageLoss: number;
    dustReady: number;
  };
}

export function MaterialFlowDashboard() {
  const [data, setData] = useState<DashboardData>({
    activeTransactions: [],
    overdueItems: [],
    lossAlerts: [],
    productivityMetrics: [],
    dustBatches: [],
    summary: {
      totalActive: 0,
      totalOverdue: 0,
      totalAlerts: 0,
      averageLoss: 0,
      dustReady: 0
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
    
    // Set up auto-refresh
    if (autoRefresh) {
      const interval = setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - in real implementation, these would be API calls
      const mockData: DashboardData = {
        activeTransactions: [
          {
            id: '1',
            type: 'metal',
            customer_name: 'Customer A',
            worker_name: 'John Smith',
            process_name: 'Setting',
            issued_weight: 25.5,
            issued_date: '2025-07-02T09:00:00Z',
            expected_return: '2025-07-02T17:00:00Z',
            status: 'active',
            priority: 'medium'
          },
          {
            id: '2',
            type: 'stone',
            customer_name: 'Customer B',
            worker_name: 'Jane Doe',
            process_name: 'Filing',
            issued_weight: 12.3,
            issued_date: '2025-07-01T14:00:00Z',
            expected_return: '2025-07-02T10:00:00Z',
            status: 'overdue',
            priority: 'high'
          }
        ],
        overdueItems: [
          {
            id: '2',
            type: 'stone',
            customer_name: 'Customer B',
            worker_name: 'Jane Doe',
            process_name: 'Filing',
            issued_weight: 12.3,
            issued_date: '2025-07-01T14:00:00Z',
            expected_return: '2025-07-02T10:00:00Z',
            status: 'overdue',
            priority: 'high'
          }
        ],
        lossAlerts: [
          {
            id: '1',
            transaction_id: 'T001',
            worker_name: 'Mike Johnson',
            process_name: 'Polishing',
            material_type: 'Gold',
            loss_percentage: 7.5,
            severity: 'critical',
            timestamp: '2025-07-02T11:30:00Z'
          }
        ],
        productivityMetrics: [
          {
            period: 'Today',
            materials_issued: 24,
            materials_received: 18,
            average_loss: 2.3,
            dust_collected: 12.5,
            efficiency_score: 92
          },
          {
            period: 'This Week',
            materials_issued: 156,
            materials_received: 142,
            average_loss: 2.1,
            dust_collected: 89.2,
            efficiency_score: 94
          }
        ],
        dustBatches: [],
        summary: {
          totalActive: 15,
          totalOverdue: 3,
          totalAlerts: 2,
          averageLoss: 2.3,
          dustReady: 5
        }
      };

      setData(mockData);
      setLastUpdated(new Date());
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickReceipt = (transactionId: string) => {
    // Navigate to receipt form with pre-filled data
    console.log('Quick receipt for transaction:', transactionId);
  };

  const handleResolveAlert = (alertId: string) => {
    // Mark alert as resolved
    console.log('Resolving alert:', alertId);
  };

  const getStatusBadge = (status: string, priority?: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800">Active</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-300';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Less than 1 hour ago';
    if (diffInHours === 1) return '1 hour ago';
    return `${diffInHours} hours ago`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Material Flow Dashboard</h2>
          <p className="text-gray-600">
            Real-time overview of all material activities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={loadDashboardData}
            disabled={isLoading}
          >
            <RefreshCwIcon className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active</p>
                <p className="text-2xl font-bold text-blue-600">{data.summary.totalActive}</p>
              </div>
              <ActivityIcon className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Overdue</p>
                <p className="text-2xl font-bold text-red-600">{data.summary.totalOverdue}</p>
              </div>
              <ClockIcon className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Alerts</p>
                <p className="text-2xl font-bold text-orange-600">{data.summary.totalAlerts}</p>
              </div>
              <AlertTriangleIcon className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Loss</p>
                <p className="text-2xl font-bold">{data.summary.averageLoss}%</p>
              </div>
              <TrendingDownIcon className="w-8 h-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Dust Ready</p>
                <p className="text-2xl font-bold text-yellow-600">{data.summary.dustReady}</p>
              </div>
              <SparklesIcon className="w-8 h-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Transactions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PackageIcon className="w-5 h-5" />
              Active Transactions
            </CardTitle>
            <CardDescription>
              Materials currently with workers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.activeTransactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className={`p-3 border-l-4 rounded-lg bg-gray-50 ${getPriorityColor(transaction.priority)}`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium">{transaction.worker_name}</span>
                        <Badge variant="outline" className="text-xs">
                          {transaction.process_name}
                        </Badge>
                        {getStatusBadge(transaction.status)}
                      </div>
                      <p className="text-sm text-gray-600">
                        {transaction.customer_name} • {transaction.issued_weight}g {transaction.type}
                      </p>
                      <p className="text-xs text-gray-500">
                        Issued: {formatTimeAgo(transaction.issued_date)}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleQuickReceipt(transaction.id)}
                    >
                      Quick Receipt
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Loss Alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangleIcon className="w-5 h-5" />
              Loss Alerts
            </CardTitle>
            <CardDescription>
              High loss transactions requiring attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.lossAlerts.map((alert) => (
                <Alert key={alert.id} className={alert.severity === 'critical' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
                  <AlertTriangleIcon className="w-4 h-4" />
                  <AlertDescription>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">
                          {alert.worker_name} - {alert.process_name}
                        </p>
                        <p className="text-sm">
                          {alert.material_type} loss: {alert.loss_percentage}%
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatTimeAgo(alert.timestamp)}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleResolveAlert(alert.id)}
                      >
                        Resolve
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
