/**
 * API Utilities
 * Common utilities for API route handlers
 * 
 * @module lib/api-utils
 */

import { NextResponse } from 'next/server';

/**
 * Creates a standardized error response for API routes
 * 
 * @param {string} message - Error message to return
 * @param {number} status - HTTP status code
 * @returns {NextResponse} Formatted error response
 * 
 * @example
 * // Return a 404 error
 * return getErrorResponse('Resource not found', 404);
 */
export function getErrorResponse(message: string, status: number = 500): NextResponse {
  return NextResponse.json(
    { 
      error: message,
      success: false,
      timestamp: new Date().toISOString()
    },
    { status }
  );
}

/**
 * Creates a standardized success response for API routes
 * 
 * @param {any} data - Data to return in the response
 * @param {number} status - HTTP status code
 * @returns {NextResponse} Formatted success response
 * 
 * @example
 * // Return a 201 created response
 * return getSuccessResponse({ id: '123' }, 201);
 */
export function getSuccessResponse(data: any, status: number = 200): NextResponse {
  return NextResponse.json(
    {
      data,
      success: true,
      timestamp: new Date().toISOString()
    },
    { status }
  );
}

/**
 * Parses a query parameter to ensure it's a string
 * 
 * @param {string | string[] | undefined} param - Query parameter to parse
 * @returns {string | undefined} Parsed parameter
 */
export function parseQueryParam(param: string | string[] | undefined): string | undefined {
  if (Array.isArray(param)) {
    return param[0];
  }
  return param;
}

/**
 * Validates required fields in a request body
 * 
 * @param {object} body - Request body to validate
 * @param {string[]} requiredFields - List of required field names
 * @returns {{ isValid: boolean, missingFields: string[] }} Validation result
 * 
 * @example
 * const { isValid, missingFields } = validateRequiredFields(body, ['name', 'email']);
 * if (!isValid) {
 *   return getErrorResponse(`Missing required fields: ${missingFields.join(', ')}`, 400);
 * }
 */
export function validateRequiredFields(body: Record<string, any>, requiredFields: string[]): { 
  isValid: boolean; 
  missingFields: string[] 
} {
  const missingFields = requiredFields.filter(field => !body[field]);
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}
