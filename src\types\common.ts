/**
 * Common Type Definitions
 * Core type definitions used throughout the application
 * @module types/common
 */

/**
 * Base interface for master data entities
 * @interface BaseMaster
 * @property {string} id - Unique identifier
 * @property {string} description - Human-readable description
 * @property {string} createdAt - Creation timestamp
 * @property {string} updatedAt - Last update timestamp
 */
export interface BaseMaster {
  id: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Base interface for all database entities
 * @interface BaseEntity
 * @property {string | null} created_at - Creation timestamp
 * @property {string | null} updated_at - Last update timestamp
 */
export interface BaseEntity {
  created_at: string | null;
  updated_at: string | null;
}

/**
 * Item type definition for jewelry items
 * @interface ItemType
 * @extends {BaseEntity}
 * @property {string} item_type_id - Unique identifier
 * @property {string} description - Item type description
 * @property {number} average_processing_time - Average time to process this item type
 */
export interface ItemType extends BaseEntity {
  item_type_id: string;
  description: string;
  average_processing_time: number;
}

/**
 * Gold karat definition
 * @interface Karat
 * @extends {BaseEntity}
 * @property {string} karat_id - Unique identifier
 * @property {string} description - Karat description (e.g., "18K", "22K")
 * @property {number} purity - Gold purity percentage
 * @property {number} standard_wastage - Expected wastage percentage
 */
export interface Karat extends BaseEntity {
  karat_id: string;
  description: string;
  purity: number;
  standard_wastage: number;
}

/**
 * Gold color definition
 * @interface GoldColour
 * @extends {BaseEntity}
 * @property {string} gold_colour_id - Unique identifier
 * @property {string} description - Color description
 * @property {number} processing_complexity_factor - Factor affecting processing time
 */
export interface GoldColour extends BaseEntity {
  gold_colour_id: string;
  description: string;
  processing_complexity_factor: number;
}

/**
 * Order category definition
 * @interface OrderCategory
 * @extends {BaseEntity}
 * @property {string} order_category_id - Unique identifier
 * @property {string} description - Category description
 * @property {number} base_processing_time - Base time for this category
 */
export interface OrderCategory extends BaseEntity {
  order_category_id: string;
  description: string;
  base_processing_time: number;
}

/**
 * Manufacturing process definition
 * @interface Process
 * @extends {BaseEntity}
 * @property {string} process_id - Unique identifier
 * @property {string} description - Process description
 * @property {number} standard_time - Standard time to complete
 * @property {string[]} required_skills - Required worker skills
 * @property {number} sequence_number - Process sequence in workflow
 * @property {boolean} is_optional - Whether process can be skipped
 */
export interface Process extends BaseEntity {
  process_id: string;
  description: string;
  standard_time: number;
  required_skills: string[];
  sequence_number: number;
  is_optional: boolean;
}

/**
 * Workshop worker definition
 * @interface Worker
 * @extends {BaseEntity}
 * @property {string} worker_id - Unique identifier
 * @property {string} name - Worker's name
 * @property {string[]} skills - Worker's skills
 * @property {number} efficiency_factor - Worker's efficiency rating
 * @property {Object} working_hours - Worker's schedule
 * @property {string} working_hours.start - Start time
 * @property {string} working_hours.end - End time
 * @property {boolean} is_active - Whether worker is currently active
 */
export interface Worker extends BaseEntity {
  worker_id: string;
  name: string;
  skills: string[];
  efficiency_factor: number;
  working_hours: {
    start: string;
    end: string;
  };
  is_active: boolean;
}

/**
 * Third-party customer definition
 * @interface ThirdPartyCustomer
 * @extends {BaseEntity}
 * @property {string} party_cust_id - Unique identifier
 * @property {string} description - Customer description
 * @property {Object} contact_details - Customer contact information
 */
export interface ThirdPartyCustomer extends BaseEntity {
  party_cust_id: string;
  description: string;
  contact_details: {
    phone?: string;
    email?: string;
    address?: string;
  };
}

/**
 * Jewelry style definition
 * @interface Style
 * @extends {BaseEntity}
 * @property {string} style_id - Unique identifier
 * @property {string} style_code - Style reference code
 * @property {string} description - Style description
 * @property {number} complexity_factor - Style complexity rating
 */
export interface Style extends BaseEntity {
  style_id: string;
  style_code: string;
  description: string;
  complexity_factor: number;
}

/**
 * Manufacturing order definition
 * Reflects the structure returned by the Supabase query in orders/[id]/page.tsx
 * @interface Order
 * @extends {BaseEntity}
 */
export interface Order extends BaseEntity {
  order_id: string;
  party_order_no?: string | null;
  style_code?: string | null; // Renamed from style_id to match DB
  item_type_id: string;
  karat_id: string;
  gold_colour_id: string;
  order_category_id: string;
  customer_id: string;
  third_party_cust_id?: string | null;
  issue_date: string;
  party_delivery_date?: string | null; // Added from DB schema
  expected_delivery_date: string;
  // estimated_completion_date removed - not in DB
  // actual_completion_date removed - not in DB
  polki_quality?: string | null; // Added from DB schema
  diamond_quality?: string | null; // Added from DB schema
  diamond_wt_expected?: number | null; // Added from DB schema
  gold_wt_expected?: number | null; // Added from DB schema
  reference_image_url?: string | null; // Added from DB schema
  metal_received?: boolean | null;
  diamonds_received?: boolean | null;
  polki_received?: boolean | null;
  tags?: string[] | null; // Added from DB schema
  remarks?: string | null;
  order_status?: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'active' | string | null; // Renamed from status, allow string for flexibility
  qr_code?: string | null; // Added from DB schema

  // Related entities (adjust based on actual queries)
  customer?: { description: string } | null; 
  third_party_customer?: { description: string } | null; 
  item_type?: { description: string } | null;
  karat?: { description: string } | null;
  metal_colour?: { description: string } | null;
  order_category?: { description: string } | null;
}

/**
 * Order process tracking definition
 * @interface OrderProcess
 * @extends {BaseEntity}
 * @property {string} order_process_id - Unique identifier
 * @property {string} order_id - Order reference
 * @property {string} process_id - Process reference
 * @property {string[]} [worker_id] - Assigned worker reference
 * @property {string} planned_start_date - Scheduled start date
 * @property {string} planned_end_date - Scheduled end date
 * @property {string} [actual_start_date] - Actual start date
 * @property {string} [actual_end_date] - Actual completion date
 * @property {'pending' | 'in_progress' | 'completed' | 'cancelled'} status - Process status
 * @property {string} [notes] - Process notes
 */
export interface OrderProcess extends BaseEntity {
  order_process_id: string;
  order_id: string;
  process_id: string;
  worker_id?: string;
  planned_start_date: string;
  planned_end_date: string;
  actual_start_date?: string;
  actual_end_date?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
}

/**
 * Column definition for data tables
 * @interface Column
 * @property {keyof T} key - Column key
 * @property {string} label - Column label
 */
export interface Column<T> {
  key: keyof T;
  label: string;
}

/**
 * Form field definition for dynamic forms
 * @interface FormField
 * @property {string} id - Field identifier
 * @property {string} label - Field label
 * @property {'text' | 'number' | 'select' | 'checkbox' | 'date' | 'time' | 'textarea'} type - Field type
 * @property {boolean} [required] - Whether field is required
 * @property {Array<{value: string, label: string}>} [options] - Options for select fields
 * @property {string} [pattern] - Regex pattern for validation
 * @property {number} [maxLength] - Maximum length for text input
 * @property {string} [helpText] - Help text to display with the field
 * @property {string} [placeholder] - Placeholder text for input fields
 * @property {any} [defaultValue] - Default value for the field
 * @property {number} [min] - Minimum value for number input
 * @property {number} [max] - Maximum value for number input
 * @property {number} [step] - Step value for number input
 * @property {string} [dependsOn] - Defines dependency on another field ID
 */
export interface FormField {
  id: string;
  label: string;
  type: 'text' | 'number' | 'select' | 'checkbox' | 'date' | 'time' | 'textarea';
  required?: boolean;
  options?: Array<{
    value: string;
    label: string;
  }>;
  pattern?: string;
  maxLength?: number;
  helpText?: string;
  placeholder?: string;
  defaultValue?: any;
  min?: number;
  max?: number;
  step?: number;
  dependsOn?: string;
}

/**
 * Entity status type
 * @type {EntityStatus}
 */
export type EntityStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';
