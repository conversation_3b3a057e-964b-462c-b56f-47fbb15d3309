/**
 * Orders API Route Handler
 * Manages jewelry orders with role-based access control
 * Implements proper order ID generation using item type suffix
 * @module api/orders
 */

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';
import { createOrder as createOrderService } from '@/services/orderService';
import { Order } from '@/types/orders';

/**
 * Response type for GET /api/orders
 */
type GetOrdersResponse = Order[] | { error: string };

/**
 * Response type for POST /api/orders
 */
type PostOrderResponse = Order | { error: string };

/**
 * GET handler for orders
 * Retrieves orders with role-based filtering and related data
 * 
 * @access Public - Available to admin, data_entry, and customer roles
 * @route GET /api/orders
 * @header {string} x-user-role - User's role for access control
 * @header {string} x-user-id - User's ID
 * @header {string} x-user-email - User's email (required for customer role)
 * @returns {Promise<NextResponse<GetOrdersResponse>>} List of orders with related data
 * 
 * @example
 * // Get all orders (admin/data_entry)
 * GET /api/orders
 * Response: [
 *   {
 *     "order_id": "123",
 *     "item_type_id": "RG",
 *     "item_type_mast": { "description": "Ring" },
 *     "karat_mast": { "description": "22K" },
 *     "gold_colour_mast": { "description": "Yellow" },
 *     "order_category_mast": { "description": "Regular" },
 *     "third_party_cust_mast": {
 *       "description": "Customer Name",
 *       "contact_details": { "email": "<EMAIL>" }
 *     }
 *   },
 *   ...
 * ]
 * 
 * // Get customer-specific orders (customer role)
 * GET /api/orders
 * Headers: {
 *   "x-user-role": "customer",
 *   "x-user-email": "<EMAIL>"
 * }
 */
export const GET = withAuth(['admin', 'data_entry', 'customer'])(
  async (req: NextRequest): Promise<NextResponse<GetOrdersResponse>> => {
    try {
      console.log('GET /api/orders - Request received');
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const userRole = req.headers.get('x-user-role');
      const userId = req.headers.get('x-user-id');
      
      // Parse query parameters
      const url = new URL(req.url);
      const customerId = url.searchParams.get('customerId');
      console.log('Customer ID from query params:', customerId);

      // First, let's check if the customer exists
      if (customerId) {
        const { data: customerCheck, error: customerError } = await supabase
          .from('third_party_cust_mast')
          .select('party_cust_id, description')
          .eq('party_cust_id', customerId)
          .single();
        
        console.log('Customer check result:', customerCheck, customerError);
      }

      // Let's also check if there are any orders for this customer in the database
      if (customerId) {
        const { data: orderCheck, error: orderError } = await supabase
          .from('orders')
          .select('order_id, customer_id, third_party_cust_id')
          .eq('customer_id', customerId);
        
        console.log('Order check result:', orderCheck, orderError);
      }

      let query = supabase.from('orders').select(`
        *,
        item_type_mast (description),
        karat_mast (description),
        gold_colour_mast (description),
        order_category_mast (description),
        third_party_cust_mast (description)
      `);

      // Filter by customerId if provided
      if (customerId) {
        console.log('Filtering orders by customer ID:', customerId);
        query = query.eq('customer_id', customerId);
      }
      // Apply role-based filters if customerId is not provided
      else if (userRole === 'customer') {
        const { data: customerData } = await supabase
          .from('third_party_cust_mast')
          .select('party_cust_id')
          .eq('contact_details->email', req.headers.get('x-user-email'))
          .single();

        if (customerData) {
          query = query.eq('customer_id', customerData.party_cust_id);
        }
      }

      console.log('Executing query for orders');
      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST handler for orders
 * Creates a new order
 * 
 * @access Restricted - Available to admin and data_entry roles
 * @route POST /api/orders
 * @body {object} data Order data
 * @body {string} data.item_type_id - Reference to item type
 * @body {string} data.karat_id - Reference to karat type
 * @body {string} data.gold_colour_id - Reference to gold color
 * @body {string} data.order_category_id - Reference to order category
 * @body {string} data.third_party_cust_id - Reference to customer
 * @body {number} data.gross_wt - Gross weight of the item
 * @body {number} data.net_wt - Net weight of the item
 * @body {string} [data.notes] - Additional notes for the order
 * 
 * @example
 * ```typescript
 * // Create a new order
 * POST /api/orders
 * {
 *   "item_type_id": "RG",
 *   "karat_id": "22K",
 *   "gold_colour_id": "YG",
 *   "order_category_id": "REG",
 *   "third_party_cust_id": "CUST123",
 *   "gross_wt": 12.5,
 *   "net_wt": 11.8,
 *   "notes": "Customer wants traditional design"
 * }
 * ```
 */
export const POST = withAuth(['admin', 'data_entry'])(
  async (req: NextRequest): Promise<NextResponse<PostOrderResponse>> => {
    try {
      const body = await req.json();
      
      // Validate required fields
      if (!body.item_type_id) {
        return NextResponse.json(
          { error: 'item_type_id is required for order creation' },
          { status: 400 }
        );
      }
      
      // Call the enhanced order service to handle order ID generation
      const data = await createOrderService(body);
      
      return NextResponse.json(data);
    } catch (error) {
      console.error('Error creating order:', error);
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);
