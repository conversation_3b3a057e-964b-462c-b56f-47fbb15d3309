'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { supabase } from '@/lib/db';
import { styleQueries } from '@/db/queries/styles';
import type { Tables } from '@/db/types';

/**
 * Style interface matching the database schema
 * Using generated types for better consistency
 */
type Style = Tables['styles_mast']['Row'];

/**
 * Item Type interface for reference
 */
interface ItemType {
    item_type_id: string;
    description: string;
}

/**
 * Purity interface for reference (updated from Karat)
 */
interface Karat {
    purity_id: string;
    description: string;
}

/**
 * Third Party Customer interface for reference
 */
interface ThirdPartyCustomer {
    party_cust_id: string;
    description: string;
}

interface StyleFormProps {
    style?: Style | null;
    onSubmit: (data: Style) => void;
    onCancel: () => void;
}

/**
 * Form fields interface derived from generated types
 * Omit fields managed automatically (like IDs, timestamps)
 */
type StyleFormFields = Omit<Tables['styles_mast']['Insert'], 'style_id' | 'created_at' | 'updated_at'>;

export default function StyleForm({ style, onSubmit, onCancel }: StyleFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [itemTypes, setItemTypes] = useState<ItemType[]>([]);
    const [karats, setKarats] = useState<Karat[]>([]);
    const [customers, setCustomers] = useState<ThirdPartyCustomer[]>([]);

    /**
     * Fetch reference data on component mount
     */
    useEffect(() => {
        async function fetchReferenceData() {
            try {
                // Fetch item types
                const { data: itemTypesData, error: itemTypesError } = await supabase
                    .from('item_type_mast')
                    .select('item_type_id, description')
                    .order('description');
                
                if (itemTypesError) throw itemTypesError;
                setItemTypes(itemTypesData || []);
                
                // Fetch purities
                const { data: puritiesData, error: puritiesError } = await supabase
                    .from('purity_mast')
                    .select('purity_id, description')
                    .order('description');
                
                if (puritiesError) throw puritiesError;
                setKarats(puritiesData || []);
                
                // Fetch third party customers
                const { data: customersData, error: customersError } = await supabase
                    .from('third_party_cust_mast')
                    .select('party_cust_id, description')
                    .order('description');
                
                if (customersError) throw customersError;
                setCustomers(customersData || []);
            } catch (error) {
                console.error('Error fetching reference data:', error);
                toast.error('Failed to load reference data');
            }
        }
        
        fetchReferenceData();
    }, []);

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<StyleFormFields>({
        defaultValues: {
            item_type_id: style?.item_type_id || '',
            party_id: style?.party_id || '',
            net_wt: style?.net_wt || 0,
            net_wt_kt: style?.net_wt_kt || '',
            estimated_processing_time: style?.estimated_processing_time || 0,
            processing_notes: style?.processing_notes || '',
        },
    });

    const onSubmitForm = async (data: StyleFormFields) => {
        setIsSubmitting(true);
        try {
            // Prepare style data using types for safety
            // BaseQueries handles timestamps automatically
            const styleData: Tables['styles_mast']['Insert'] | Tables['styles_mast']['Update'] = {
                item_type_id: data.item_type_id || null,
                party_id: data.party_id || null,
                net_wt: data.net_wt,
                net_wt_kt: data.net_wt_kt || null,
                estimated_processing_time: data.estimated_processing_time,
                processing_notes: data.processing_notes || null,
                // No need to manually set updated_at
            };

            let savedStyle: Style;
            if (style?.style_id) {
                // Update existing style using the query class
                savedStyle = await styleQueries.updateStyle(style.style_id, styleData as Tables['styles_mast']['Update']);
                toast.success('Style updated successfully');
            } else {
                // Create new style using the query class
                // Need to cast as Insert type includes non-optional fields potentially missing from form
                savedStyle = await styleQueries.createStyle(styleData as Tables['styles_mast']['Insert']);
                toast.success('Style created successfully');
            }

            onSubmit(savedStyle);
            onCancel(); // Close form on success
        } catch (error) {
            // Error handled by styleQueries and global handler, but log contextually
            console.error('Error saving style in form:', error);
            toast.error('Failed to save style. See console for details.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start justify-center p-4 overflow-y-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-lg my-8">
                <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                    {style ? 'Edit Style' : 'New Style'}
                </h2>

                <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-5">
                    {/* Item Type Selection */}
                    <div className="space-y-2">
                        <label htmlFor="item_type_id" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Item Type
                        </label>
                        <select
                            id="item_type_id"
                            {...register('item_type_id')}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        >
                            <option value="">Select Item Type</option>
                            {itemTypes.map(type => (
                                <option key={type.item_type_id} value={type.item_type_id}>
                                    {type.description}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Customer Selection */}
                    <div className="space-y-2">
                        <label htmlFor="party_id" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Customer
                        </label>
                        <select
                            id="party_id"
                            {...register('party_id')}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        >
                            <option value="">Select Customer</option>
                            {customers.map(customer => (
                                <option key={customer.party_cust_id} value={customer.party_cust_id}>
                                    {customer.description}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Net Weight */}
                    <div className="space-y-2">
                        <label htmlFor="net_wt" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Net Weight (g) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="net_wt"
                            step="0.01"
                            min="0"
                            {...register('net_wt', { 
                                required: 'Net weight is required',
                                min: {
                                    value: 0,
                                    message: 'Weight must be greater than 0'
                                }
                            })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        />
                        {errors.net_wt && (
                            <p className="text-red-500 text-sm">{errors.net_wt.message}</p>
                        )}
                    </div>

                    {/* Purity Selection (stored as net_wt_kt for compatibility) */}
                    <div className="space-y-2">
                        <label htmlFor="net_wt_kt" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Purity <span className="text-red-500">*</span>
                        </label>
                        <select
                            id="net_wt_kt"
                            {...register('net_wt_kt', { required: 'Purity selection is required' })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        >
                            <option value="">Select Purity</option>
                            {karats.map(karat => (
                                <option key={karat.purity_id} value={karat.purity_id}>
                                    {karat.description}
                                </option>
                            ))}
                        </select>
                        {errors.net_wt_kt && (
                            <p className="text-red-500 text-sm">{errors.net_wt_kt.message}</p>
                        )}
                    </div>

                    {/* Estimated Processing Time */}
                    <div className="space-y-2">
                        <label htmlFor="estimated_processing_time" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Estimated Processing Time (hrs) <span className="text-red-500">*</span>
                        </label>
                        <input
                            type="number"
                            id="estimated_processing_time"
                            step="0.5"
                            min="0.5"
                            {...register('estimated_processing_time', { 
                                required: 'Processing time is required',
                                min: {
                                    value: 0.5,
                                    message: 'Time must be at least 0.5 hours'
                                }
                            })}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        />
                        {errors.estimated_processing_time && (
                            <p className="text-red-500 text-sm">{errors.estimated_processing_time.message}</p>
                        )}
                    </div>

                    {/* Processing Notes */}
                    <div className="space-y-2">
                        <label htmlFor="processing_notes" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                            Processing Notes
                        </label>
                        <textarea
                            id="processing_notes"
                            {...register('processing_notes')}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                        />
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-2 pt-4">
                        <button
                            type="button"
                            onClick={onCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {isSubmitting ? 'Saving...' : style ? 'Update' : 'Create'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
