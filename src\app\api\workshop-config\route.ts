import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/db';

// GET - Fetch workshop configuration
export async function GET() {
  try {
    // Since workshop_config table doesn't exist yet, return default configuration
    const defaultConfig = {
      config_id: 'default',
      working_hours_per_day: 8,
      working_days_per_week: 6,
      shift_start_time: '09:00',
      shift_end_time: '18:00',
      break_duration_minutes: 60,
      weekend_days: ['Sunday'],
      buffer_hours_per_day: 1,
      allow_overtime: true,
      max_overtime_hours: 4,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return NextResponse.json(defaultConfig);
  } catch (error) {
    console.error('Error fetching workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workshop configuration' },
      { status: 500 }
    );
  }
}

// POST - Create workshop configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Since workshop_config table doesn't exist yet, return the submitted data with timestamps
    const configData = {
      ...body,
      config_id: body.config_id || 'default',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    return NextResponse.json(configData);
  } catch (error) {
    console.error('Error creating workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to create workshop configuration' },
      { status: 500 }
    );
  }
}

// PUT - Update workshop configuration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { config_id, ...updateData } = body;

    if (!config_id) {
      return NextResponse.json(
        { error: 'config_id is required' },
        { status: 400 }
      );
    }

    // Since workshop_config table doesn't exist yet, return the updated data with timestamp
    const configData = {
      ...updateData,
      config_id,
      updated_at: new Date().toISOString()
    };

    return NextResponse.json(configData);
  } catch (error) {
    console.error('Error updating workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to update workshop configuration' },
      { status: 500 }
    );
  }
}

// DELETE - Delete workshop configuration
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const config_id = searchParams.get('config_id');

    if (!config_id) {
      return NextResponse.json(
        { error: 'config_id is required' },
        { status: 400 }
      );
    }

    // Since workshop_config table doesn't exist yet, just return success
    return NextResponse.json({
      success: true,
      message: 'Workshop configuration deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to delete workshop configuration' },
      { status: 500 }
    );
  }
}
