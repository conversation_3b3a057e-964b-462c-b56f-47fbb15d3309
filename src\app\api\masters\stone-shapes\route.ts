import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data, error } = await supabase
      .from('stone_shape_mast')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching stone shapes:', error);
      return NextResponse.json({ error: 'Failed to fetch stone shapes' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-shapes API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    const { data, error } = await supabase
      .from('stone_shape_mast')
      .insert([{
        name: body.name,
        description: body.description,
        is_active: true
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating stone shape:', error);
      return NextResponse.json({ error: 'Failed to create stone shape' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-shapes POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Stone shape ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('stone_shape_mast')
      .update({
        name: body.name,
        description: body.description,
        updated_at: new Date().toISOString()
      })
      .eq('shape_id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating stone shape:', error);
      return NextResponse.json({ error: 'Failed to update stone shape' }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in stone-shapes PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Stone shape ID is required' }, { status: 400 });
    }

    const { error } = await supabase
      .from('stone_shape_mast')
      .update({ is_active: false })
      .eq('shape_id', id);

    if (error) {
      console.error('Error deleting stone shape:', error);
      return NextResponse.json({ error: 'Failed to delete stone shape' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in stone-shapes DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
