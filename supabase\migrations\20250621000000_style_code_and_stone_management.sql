-- Migration: Style Code Management and Stone Inventory Enhancement
-- Date: 2025-06-21
-- Description: Updates to support style code management, stone inventory, and polki findings

-- =====================================================
-- 1. STYLE CODE MANAGEMENT UPDATES
-- =====================================================

-- Update styles_mast table to support style code management
ALTER TABLE public.styles_mast 
ADD COLUMN IF NOT EXISTS style_code varchar(30) UNIQUE,
ADD COLUMN IF NOT EXISTS is_repeat_style boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS parent_style_id uuid REFERENCES public.styles_mast(style_id),
ADD COLUMN IF NOT EXISTS complexity_level integer CHECK (complexity_level BETWEEN 1 AND 5),
ADD COLUMN IF NOT EXISTS design_notes text,
ADD COLUMN IF NOT EXISTS cad_required boolean DEFAULT true,
ADD COLUMN IF NOT EXISTS cam_required boolean DEFAULT true;

-- Update orders table to support better style code linking
-- First, drop any existing foreign key constraint on style_code if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints
               WHERE constraint_name = 'orders_style_code_fkey'
               AND table_name = 'orders') THEN
        ALTER TABLE public.orders DROP CONSTRAINT orders_style_code_fkey;
    END IF;
END $$;

-- Now safely alter the column type and add new columns
ALTER TABLE public.orders
ALTER COLUMN style_code TYPE varchar(30),
ADD COLUMN IF NOT EXISTS complexity_level integer CHECK (complexity_level BETWEEN 1 AND 5),
ADD COLUMN IF NOT EXISTS is_repeat_order boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS reference_order_id uuid REFERENCES public.orders(order_id);

-- =====================================================
-- 2. STONE QUALITY MASTER DATA
-- =====================================================

-- Add stone quality master table
CREATE TABLE IF NOT EXISTS public.stone_quality_mast (
  quality_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name varchar NOT NULL,
  description text,
  grade_order integer, -- For sorting (1=highest, 5=lowest)
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- Insert default quality grades
INSERT INTO public.stone_quality_mast (name, description, grade_order) VALUES
('Standard', 'Standard quality for customer goods', 3),
('VVS', 'Very Very Slightly Included', 1),
('VS', 'Very Slightly Included', 2),
('SI', 'Slightly Included', 4),
('I', 'Included', 5)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 3. ENHANCED STONE INVENTORY TABLES
-- =====================================================

-- Create comprehensive stone inventory table
CREATE TABLE IF NOT EXISTS public.stone_inventory (
  inventory_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES public.customer_mast(customer_id),
  order_id uuid REFERENCES public.orders(order_id), -- Can be allocated to specific order
  stone_type_id uuid NOT NULL REFERENCES public.stone_type_mast(stone_type_id),
  stone_shape_id uuid NOT NULL REFERENCES public.stone_shape_mast(shape_id),
  stone_size_id uuid NOT NULL REFERENCES public.stone_size_mast(size_id),
  stone_quality_id uuid NOT NULL REFERENCES public.stone_quality_mast(quality_id),
  quantity integer NOT NULL CHECK (quantity >= 0),
  total_carat_weight numeric(10,4) NOT NULL CHECK (total_carat_weight >= 0),
  location varchar NOT NULL CHECK (location IN ('Customers', 'Safe', 'Central', 'External Vendors', 'Floor')),
  status varchar DEFAULT 'available' CHECK (status IN ('available', 'allocated', 'issued', 'consumed', 'returned', 'damaged', 'lost')),
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  
  -- Ensure unique combination per customer
  UNIQUE(customer_id, stone_type_id, stone_shape_id, stone_size_id, stone_quality_id, location, order_id)
);

-- =====================================================
-- 4. POLKI FINDINGS MANAGEMENT
-- =====================================================

-- Create findings master table for polki assemblies
CREATE TABLE IF NOT EXISTS public.findings_mast (
  finding_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES public.customer_mast(customer_id),
  order_id uuid REFERENCES public.orders(order_id), -- Can be allocated to specific order
  finding_type varchar NOT NULL DEFAULT 'Polki Assembly',
  description text NOT NULL,
  gross_weight_grams numeric(10,4) NOT NULL CHECK (gross_weight_grams > 0), -- Including foil
  location varchar NOT NULL CHECK (location IN ('Customers', 'Safe', 'Central', 'External Vendors', 'Floor')),
  status varchar DEFAULT 'available' CHECK (status IN ('available', 'allocated', 'issued', 'consumed', 'returned')),
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- Create polki stone details table (child of findings)
CREATE TABLE IF NOT EXISTS public.finding_stone_details (
  detail_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  finding_id uuid NOT NULL REFERENCES public.findings_mast(finding_id) ON DELETE CASCADE,
  stone_type_id uuid NOT NULL REFERENCES public.stone_type_mast(stone_type_id),
  stone_shape_id uuid NOT NULL REFERENCES public.stone_shape_mast(shape_id),
  stone_size_id uuid NOT NULL REFERENCES public.stone_size_mast(size_id),
  pieces integer NOT NULL CHECK (pieces > 0),
  carat_weight numeric(10,4) NOT NULL CHECK (carat_weight > 0), -- Stone weight only, excluding foil
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 5. PROCESS CONFIGURATION UPDATES
-- =====================================================

-- Update process_mast table to support complexity-based timing and material requirements
ALTER TABLE public.process_mast
ADD COLUMN IF NOT EXISTS materials_issued jsonb, -- What materials can be issued for this process
ADD COLUMN IF NOT EXISTS materials_returned jsonb, -- What materials can be returned from this process
ADD COLUMN IF NOT EXISTS stones_required boolean DEFAULT false,
ADD COLUMN IF NOT EXISTS findings_required boolean DEFAULT false;

-- Update complexity_hours_json structure to support our 5-level system
-- This will store: {"1": 3.0, "2": 4.5, "3": 7.5, "4": 12.0, "5": 15.0} for 10g base weight

-- =====================================================
-- 6. MATERIAL ISSUE/RECEIPT TRACKING TABLES
-- =====================================================

-- Create material transactions table for issue/receipt tracking
CREATE TABLE IF NOT EXISTS public.material_transactions (
  transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id uuid NOT NULL REFERENCES public.orders(order_id),
  process_id uuid NOT NULL REFERENCES public.process_mast(process_id),
  worker_id uuid REFERENCES public.worker_mast(worker_id),
  transaction_type varchar NOT NULL CHECK (transaction_type IN ('issue', 'receipt')),
  transaction_date timestamptz DEFAULT CURRENT_TIMESTAMP,
  gross_weight_before numeric(10,4),
  gross_weight_after numeric(10,4),
  net_weight_before numeric(10,4),
  net_weight_after numeric(10,4),
  notes text,
  created_by uuid REFERENCES public.user_roles(user_id),
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- Create stone transaction details
CREATE TABLE IF NOT EXISTS public.stone_transaction_details (
  detail_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid NOT NULL REFERENCES public.material_transactions(transaction_id) ON DELETE CASCADE,
  stone_inventory_id uuid REFERENCES public.stone_inventory(inventory_id),
  stone_type_id uuid NOT NULL REFERENCES public.stone_type_mast(stone_type_id),
  stone_shape_id uuid NOT NULL REFERENCES public.stone_shape_mast(shape_id),
  stone_size_id uuid NOT NULL REFERENCES public.stone_size_mast(size_id),
  quantity_issued integer DEFAULT 0,
  quantity_returned integer DEFAULT 0,
  quantity_consumed integer DEFAULT 0,
  quantity_damaged integer DEFAULT 0,
  quantity_lost integer DEFAULT 0,
  carat_weight_issued numeric(10,4) DEFAULT 0,
  carat_weight_returned numeric(10,4) DEFAULT 0,
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- Create findings transaction details
CREATE TABLE IF NOT EXISTS public.finding_transaction_details (
  detail_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid NOT NULL REFERENCES public.material_transactions(transaction_id) ON DELETE CASCADE,
  finding_id uuid REFERENCES public.findings_mast(finding_id),
  quantity_issued integer DEFAULT 0,
  quantity_returned integer DEFAULT 0,
  weight_issued_grams numeric(10,4) DEFAULT 0,
  weight_returned_grams numeric(10,4) DEFAULT 0,
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 7. DUST MANAGEMENT TABLES
-- =====================================================

-- Create dust parcels table
CREATE TABLE IF NOT EXISTS public.dust_parcels_enhanced (
  parcel_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  parcel_number varchar NOT NULL UNIQUE, -- Sequential: 0001, 0002, etc.
  transaction_id uuid REFERENCES public.material_transactions(transaction_id),
  worker_id uuid REFERENCES public.worker_mast(worker_id),
  process_id uuid REFERENCES public.process_mast(process_id),
  dust_type varchar NOT NULL CHECK (dust_type IN ('filing', 'setting', 'polish', 'bob', 'mixed')),
  weight_grams numeric(10,4) NOT NULL CHECK (weight_grams > 0),
  estimated_recovery_pct numeric(5,2) NOT NULL CHECK (estimated_recovery_pct BETWEEN 0 AND 100),
  actual_recovery_pct numeric(5,2), -- Set after refining
  refine_batch_id uuid REFERENCES public.dust_refine_batches(batch_id),
  status varchar DEFAULT 'collected' CHECK (status IN ('collected', 'batched', 'refined')),
  collection_date timestamptz DEFAULT CURRENT_TIMESTAMP,
  notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 8. INDEXES FOR PERFORMANCE
-- =====================================================

-- Indexes for stone inventory
CREATE INDEX IF NOT EXISTS idx_stone_inventory_customer ON public.stone_inventory(customer_id);
CREATE INDEX IF NOT EXISTS idx_stone_inventory_order ON public.stone_inventory(order_id);
CREATE INDEX IF NOT EXISTS idx_stone_inventory_location ON public.stone_inventory(location);
CREATE INDEX IF NOT EXISTS idx_stone_inventory_status ON public.stone_inventory(status);

-- Indexes for material transactions
CREATE INDEX IF NOT EXISTS idx_material_transactions_order ON public.material_transactions(order_id);
CREATE INDEX IF NOT EXISTS idx_material_transactions_process ON public.material_transactions(process_id);
CREATE INDEX IF NOT EXISTS idx_material_transactions_worker ON public.material_transactions(worker_id);
CREATE INDEX IF NOT EXISTS idx_material_transactions_date ON public.material_transactions(transaction_date);

-- Indexes for dust parcels
CREATE INDEX IF NOT EXISTS idx_dust_parcels_worker ON public.dust_parcels_enhanced(worker_id);
CREATE INDEX IF NOT EXISTS idx_dust_parcels_process ON public.dust_parcels_enhanced(process_id);
CREATE INDEX IF NOT EXISTS idx_dust_parcels_type ON public.dust_parcels_enhanced(dust_type);
CREATE INDEX IF NOT EXISTS idx_dust_parcels_status ON public.dust_parcels_enhanced(status);

-- =====================================================
-- 9. ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all new tables
ALTER TABLE public.stone_quality_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.findings_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.finding_stone_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.material_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_transaction_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.finding_transaction_details ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dust_parcels_enhanced ENABLE ROW LEVEL SECURITY;

-- Enable RLS on existing tables (fix the warnings)
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.styles_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.worker_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.process_mast ENABLE ROW LEVEL SECURITY;

-- Master data tables (read-only for most users)
ALTER TABLE public.item_type_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.karat_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.gold_colour_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_category_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_type_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_shape_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_size_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.third_party_cust_mast ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 10. RLS POLICIES FOR AUTHENTICATED USERS
-- =====================================================

-- Policy for authenticated users to access master data (read-only)
CREATE POLICY "Allow authenticated read access to master data" ON public.stone_quality_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to item types" ON public.item_type_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to karats" ON public.karat_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to gold colors" ON public.gold_colour_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to order categories" ON public.order_category_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to stone types" ON public.stone_type_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to stone shapes" ON public.stone_shape_mast
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated read access to stone sizes" ON public.stone_size_mast
    FOR SELECT TO authenticated USING (true);

-- Policy for authenticated users to manage their data
CREATE POLICY "Allow authenticated users to manage customers" ON public.customer_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage third party customers" ON public.third_party_cust_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage orders" ON public.orders
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage styles" ON public.styles_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage workers" ON public.worker_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage processes" ON public.process_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Policy for stone inventory (customer-specific access)
CREATE POLICY "Allow authenticated users to manage stone inventory" ON public.stone_inventory
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Policy for findings management
CREATE POLICY "Allow authenticated users to manage findings" ON public.findings_mast
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage finding details" ON public.finding_stone_details
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Policy for material transactions
CREATE POLICY "Allow authenticated users to manage material transactions" ON public.material_transactions
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage stone transaction details" ON public.stone_transaction_details
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage finding transaction details" ON public.finding_transaction_details
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Policy for dust management
CREATE POLICY "Allow authenticated users to manage dust parcels" ON public.dust_parcels_enhanced
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Policy for user roles
CREATE POLICY "Allow authenticated users to read user roles" ON public.user_roles
    FOR SELECT TO authenticated USING (true);

-- =====================================================
-- 11. COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.stone_inventory IS 'Customer-specific stone inventory with location and order allocation tracking';
COMMENT ON TABLE public.findings_mast IS 'Polki assemblies and other findings with gross weight including foil';
COMMENT ON TABLE public.finding_stone_details IS 'Individual stone details within polki assemblies';
COMMENT ON TABLE public.material_transactions IS 'Material issue and receipt transactions for each process';
COMMENT ON TABLE public.dust_parcels_enhanced IS 'Enhanced dust parcel tracking with recovery rate management';
