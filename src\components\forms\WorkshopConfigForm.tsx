'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

// Workshop Configuration interface
interface WorkshopConfig {
  config_id: string;
  working_hours_per_day?: number;
  working_days_per_week?: number;
  shift_start_time?: string;
  shift_end_time?: string;
  break_duration_minutes?: number;
  weekend_days?: string[];
  buffer_hours_per_day?: number;
  allow_overtime?: boolean;
  max_overtime_hours?: number;
  created_at: string;
  updated_at: string;
}

interface WorkshopConfigFormData extends Omit<WorkshopConfig, 'config_id' | 'created_at' | 'updated_at'> {}

export function WorkshopConfigForm() {
    const [loading, setLoading] = useState(false);
    const [currentConfig, setCurrentConfig] = useState<WorkshopConfig | null>(null);
    const { toast } = useToast();

    const { register, handleSubmit, reset, watch, setValue, formState: { errors } } = useForm<WorkshopConfigFormData>();
    const allowOvertime = watch('allow_overtime');

    useEffect(() => {
        fetchCurrentConfig();
    }, []);

    const fetchCurrentConfig = async () => {
        try {
            const response = await fetch('/api/workshop-config');
            const data = await response.json();
            if (data) {
                setCurrentConfig(data);
                reset(data);
            }
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to load workshop configuration',
                variant: 'destructive'
            });
        }
    };

    const onSubmit = async (data: WorkshopConfigFormData) => {
        setLoading(true);
        try {
            const response = await fetch('/api/workshop-config', {
                method: currentConfig ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) throw new Error('Failed to save configuration');

            toast({
                title: 'Success',
                description: 'Workshop configuration saved successfully',
            });
            fetchCurrentConfig();
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save workshop configuration',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
                <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">Working Hours Per Day</label>
                    <Input
                        type="number"
                        step="0.5"
                        {...register('working_hours_per_day', {
                            required: 'Working hours is required',
                            min: { value: 1, message: 'Minimum 1 hour' },
                            max: { value: 24, message: 'Maximum 24 hours' }
                        })}
                        className="w-full"
                    />
                    {errors.working_hours_per_day && (
                        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.working_hours_per_day.message}</p>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">Working Days Per Week</label>
                    <Input
                        type="number"
                        {...register('working_days_per_week', {
                            required: 'Working days is required',
                            min: { value: 1, message: 'Minimum 1 day' },
                            max: { value: 7, message: 'Maximum 7 days' }
                        })}
                        className="w-full"
                    />
                    {errors.working_days_per_week && (
                        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.working_days_per_week.message}</p>
                    )}
                </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
                <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">Shift Start Time</label>
                    <Input
                        type="time"
                        {...register('shift_start_time', { required: 'Start time is required' })}
                        className="w-full"
                    />
                    {errors.shift_start_time && (
                        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.shift_start_time.message}</p>
                    )}
                </div>

                <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">Shift End Time</label>
                    <Input
                        type="time"
                        {...register('shift_end_time', { required: 'End time is required' })}
                        className="w-full"
                    />
                    {errors.shift_end_time && (
                        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.shift_end_time.message}</p>
                    )}
                </div>
            </div>

            <div>
                <label className="block text-sm font-medium mb-2 dark:text-gray-200">Break Duration (minutes)</label>
                <Input
                    type="number"
                    {...register('break_duration_minutes', {
                        required: 'Break duration is required',
                        min: { value: 0, message: 'Minimum 0 minutes' },
                        max: { value: 120, message: 'Maximum 120 minutes' }
                    })}
                    className="w-full"
                />
                {errors.break_duration_minutes && (
                    <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.break_duration_minutes.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2 dark:text-gray-200">Weekend Days</label>
                <select
                    multiple
                    {...register('weekend_days', { required: 'Weekend days are required' })}
                    className="w-full h-32 border rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                    <option value="1">Monday</option>
                    <option value="2">Tuesday</option>
                    <option value="3">Wednesday</option>
                    <option value="4">Thursday</option>
                    <option value="5">Friday</option>
                    <option value="6">Saturday</option>
                    <option value="7">Sunday</option>
                </select>
                {errors.weekend_days && (
                    <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.weekend_days.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2 dark:text-gray-200">Buffer Hours Per Day</label>
                <Input
                    type="number"
                    step="0.5"
                    {...register('buffer_hours_per_day', {
                        required: 'Buffer hours is required',
                        min: { value: 0, message: 'Minimum 0 hours' },
                        max: { value: 4, message: 'Maximum 4 hours' }
                    })}
                    className="w-full"
                />
                {errors.buffer_hours_per_day && (
                    <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.buffer_hours_per_day.message}</p>
                )}
            </div>

            <div>
                <label className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        {...register('allow_overtime')}
                        className="form-checkbox rounded border-gray-300 dark:border-gray-600 text-blue-600 dark:text-blue-400 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700"
                    />
                    <span className="text-sm font-medium dark:text-gray-200">Allow Overtime</span>
                </label>
            </div>

            {allowOvertime && (
                <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-200">Maximum Overtime Hours</label>
                    <Input
                        type="number"
                        step="0.5"
                        {...register('max_overtime_hours', {
                            required: 'Maximum overtime hours is required when overtime is allowed',
                            min: { value: 0.5, message: 'Minimum 0.5 hours' },
                            max: { value: 4, message: 'Maximum 4 hours' }
                        })}
                        className="w-full"
                    />
                    {errors.max_overtime_hours && (
                        <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.max_overtime_hours.message}</p>
                    )}
                </div>
            )}

            <Button
                type="submit"
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Configuration'}
            </Button>
        </form>
    );
}
