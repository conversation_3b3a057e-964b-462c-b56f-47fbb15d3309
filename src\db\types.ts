import { Database } from '@/types/supabase';

export type DbResult<T> = T extends PromiseLike<infer U> ? U : never;
export type DbResultOk<T> = T extends PromiseLike<{ data: infer U }> ? Exclude<U, null> : never;

export type Tables = Database['public']['Tables'];
export type Enums = Database['public']['Enums'];

// Common table types
export type TableInsert<T extends keyof Tables> = Tables[T]['Insert'];
export type TableRow<T extends keyof Tables> = Tables[T]['Row'];
export type TableUpdate<T extends keyof Tables> = Tables[T]['Update'];
