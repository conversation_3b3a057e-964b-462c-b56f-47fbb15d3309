'use client';

import { useState } from 'react';
import { useAuthStore } from '@/store';
import { Button } from '@/components/ui/button';
import { User, LogOut } from 'lucide-react';

export function UserMenu() {
  const { user, userRole, signOut } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  const handleSignOut = async () => {
    setIsLoading(true);
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <div className="text-white text-sm">
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span>{user.email}</span>
          {userRole && (
            <span className="text-xs text-gray-300 capitalize">({userRole})</span>
          )}
        </div>
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleSignOut}
        disabled={isLoading}
        className="text-white hover:text-gray-200 hover:bg-gray-700"
      >
        <LogOut className="h-4 w-4 mr-1" />
        {isLoading ? 'Signing out...' : 'Sign out'}
      </Button>
    </div>
  );
}
