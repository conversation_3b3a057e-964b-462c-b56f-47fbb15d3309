/**
 * Format a date and time to a readable string
 */
export function formatDateTime(date: Date | string | null): string {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Format a duration in milliseconds to a readable string
 */
export function formatDuration(durationMs: number | null): string {
    if (!durationMs) return '-';
    
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        return `${days}d ${hours % 24}h`;
    }
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m`;
    }
    if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
}

/**
 * Format a date to a readable string without time
 */
export function formatDate(date: Date | string | null): string {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

/**
 * Format time to a readable string
 */
export function formatTime(date: Date | string | null): string {
    if (!date) return '-';
    const d = new Date(date);
    return d.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Format a number to a percentage string
 */
export function formatPercentage(value: number | null): string {
    if (value === null || isNaN(value)) return '-';
    return `${Math.round(value)}%`;
}

/**
 * Format a number to currency
 */
export function formatCurrency(value: number | null): string {
    if (value === null || isNaN(value)) return '-';
    return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
    }).format(value);
}

/**
 * Format a number with commas
 */
export function formatNumber(value: number | null): string {
    if (value === null || isNaN(value)) return '-';
    return new Intl.NumberFormat('en-IN').format(value);
}

/**
 * Format a weight in grams
 */
export function formatWeight(value: number | null): string {
    if (value === null || isNaN(value)) return '-';
    return `${value.toFixed(3)}g`;
}
