/**
 * Location Service
 * 
 * Handles location-based inventory operations and material transfers
 * Manages inventory across: Customers, Safe, Central, External Vendors, Floor
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';

type InventoryLocation = Database['public']['Tables']['inventory_locations']['Row'];

export interface MaterialTransferRequest {
  material_type: 'stone' | 'finding' | 'metal';
  material_id: string;
  from_location: string;
  to_location: string;
  quantity?: number;
  weight_grams?: number;
  transferred_by: string;
  transfer_reason: string;
  notes?: string;
}

export interface LocationInventoryQuery {
  location_id: string;
  material_type?: 'stone' | 'finding' | 'metal';
  customer_id?: string;
  status?: string;
}

/**
 * Transfer material between locations
 */
export async function transferMaterial(request: MaterialTransferRequest): Promise<{
  transfer_id: string;
  success: boolean;
}> {
  try {
    // 1. Validate locations exist
    await validateLocations([request.from_location, request.to_location]);

    // 2. Validate transfer rules
    await validateLocationTransfer(request);

    // 3. Create transfer record
    const transferData = {
      material_type: request.material_type,
      material_id: request.material_id,
      from_location: request.from_location,
      to_location: request.to_location,
      quantity: request.quantity,
      weight_grams: request.weight_grams,
      transfer_date: new Date().toISOString(),
      transferred_by: request.transferred_by,
      transfer_reason: request.transfer_reason,
      status: 'completed',
      notes: request.notes,
      created_at: new Date().toISOString()
    };

    const { data: transfer, error: transferError } = await supabase
      .from('material_location_transfers')
      .insert([transferData])
      .select()
      .single();

    if (transferError) throw transferError;

    // 4. Update material location based on type
    await updateMaterialLocation(
      request.material_type,
      request.material_id,
      request.to_location
    );

    return {
      transfer_id: transfer.transfer_id,
      success: true
    };

  } catch (error) {
    console.error('Error transferring material:', error);
    throw error;
  }
}

/**
 * Get inventory by location
 */
export async function getInventoryByLocation(query: LocationInventoryQuery) {
  try {
    const inventoryData: {
      stones: any[];
      findings: any[];
      metals: any[];
    } = {
      stones: [],
      findings: [],
      metals: []
    };

    // Get stones if requested
    if (!query.material_type || query.material_type === 'stone') {
      let stoneQuery = supabase
        .from('stone_inventory')
        .select(`
          *,
          stone_type:stone_type_mast(type_name),
          stone_shape:stone_shape_mast(shape_name),
          stone_size:stone_size_mast(size_name),
          stone_quality:stone_quality_mast(quality_name),
          customer:customers_mast(customer_name)
        `)
        .eq('current_location', query.location_id);

      if (query.customer_id) stoneQuery = stoneQuery.eq('customer_id', query.customer_id);
      if (query.status) stoneQuery = stoneQuery.eq('status', query.status);

      const { data: stones, error: stoneError } = await stoneQuery.order('created_at', { ascending: false });
      
      if (stoneError) throw stoneError;
      inventoryData.stones = stones || [];
    }

    // Get findings if requested
    if (!query.material_type || query.material_type === 'finding') {
      let findingQuery = supabase
        .from('findings_mast')
        .select(`
          *,
          customer:customers_mast(customer_name),
          stone_details:finding_stone_details(
            quantity,
            stone_type:stone_type_mast(type_name)
          )
        `)
        .eq('current_location', query.location_id);

      if (query.customer_id) findingQuery = findingQuery.eq('customer_id', query.customer_id);
      if (query.status) findingQuery = findingQuery.eq('status', query.status);

      const { data: findings, error: findingError } = await findingQuery.order('created_at', { ascending: false });
      
      if (findingError) throw findingError;
      inventoryData.findings = findings || [];
    }

    // Get metals if requested
    if (!query.material_type || query.material_type === 'metal') {
      let metalQuery = supabase
        .from('metal_inventory')
        .select(`
          *,
          metal_type:metal_type_mast(metal_name),
          karat:karat_mast(karat_name),
          customer:customers_mast(customer_name)
        `)
        .eq('current_location', query.location_id);

      if (query.customer_id) metalQuery = metalQuery.eq('customer_id', query.customer_id);
      if (query.status) metalQuery = metalQuery.eq('status', query.status);

      const { data: metals, error: metalError } = await metalQuery.order('created_at', { ascending: false });
      
      if (metalError) throw metalError;
      inventoryData.metals = metals || [];
    }

    return inventoryData;

  } catch (error) {
    console.error('Error fetching inventory by location:', error);
    throw error;
  }
}

/**
 * Validate location transfer business rules
 */
export async function validateLocationTransfer(request: MaterialTransferRequest): Promise<void> {
  try {
    // 1. Get location details
    const { data: fromLocation, error: fromError } = await supabase
      .from('inventory_locations')
      .select('*')
      .eq('location_id', request.from_location)
      .single();

    if (fromError) throw fromError;

    const { data: toLocation, error: toError } = await supabase
      .from('inventory_locations')
      .select('*')
      .eq('location_id', request.to_location)
      .single();

    if (toError) throw toError;

    // 2. Business rule validations
    
    // Customer materials can only go to/from Customer locations or processing areas
    if (fromLocation.location_type === 'Customer' || toLocation.location_type === 'Customer') {
      const allowedTransfers = ['Customer', 'Floor', 'Central'];
      if (!allowedTransfers.includes(fromLocation.location_type) || 
          !allowedTransfers.includes(toLocation.location_type)) {
        throw new Error('Customer materials can only be transferred between Customer, Floor, and Central locations');
      }
    }

    // External vendor materials require special handling
    if (fromLocation.location_type === 'ExternalVendor' || toLocation.location_type === 'ExternalVendor') {
      if (request.transfer_reason !== 'outsourcing' && request.transfer_reason !== 'return_from_outsourcing') {
        throw new Error('External vendor transfers require outsourcing reason');
      }
    }

    // Safe can only receive precious materials
    if (toLocation.location_type === 'Safe') {
      if (request.material_type !== 'metal' && request.material_type !== 'stone') {
        throw new Error('Safe location can only store metals and stones');
      }
    }

    // 3. Validate material exists and is available
    await validateMaterialAvailability(request);

  } catch (error) {
    console.error('Location transfer validation failed:', error);
    throw error;
  }
}

/**
 * Validate material availability for transfer
 */
async function validateMaterialAvailability(request: MaterialTransferRequest): Promise<void> {
  try {
    let materialExists = false;
    let currentLocation = '';
    let availableQuantity = 0;

    switch (request.material_type) {
      case 'stone':
        const { data: stone, error: stoneError } = await supabase
          .from('stone_inventory')
          .select('current_location, quantity, status')
          .eq('stone_inventory_id', request.material_id)
          .single();

        if (stoneError) throw stoneError;
        materialExists = !!stone;
        currentLocation = stone.current_location;
        availableQuantity = stone.quantity;
        
        if (stone.status !== 'available') {
          throw new Error('Stone is not available for transfer');
        }
        break;

      case 'finding':
        const { data: finding, error: findingError } = await supabase
          .from('findings_mast')
          .select('current_location, status')
          .eq('finding_id', request.material_id)
          .single();

        if (findingError) throw findingError;
        materialExists = !!finding;
        currentLocation = finding.current_location;
        availableQuantity = 1; // Findings are single items
        
        if (finding.status !== 'available') {
          throw new Error('Finding is not available for transfer');
        }
        break;

      case 'metal':
        const { data: metal, error: metalError } = await supabase
          .from('metal_inventory')
          .select('current_location, weight_grams, status')
          .eq('metal_inventory_id', request.material_id)
          .single();

        if (metalError) throw metalError;
        materialExists = !!metal;
        currentLocation = metal.current_location;
        availableQuantity = metal.weight_grams;
        
        if (metal.status !== 'available') {
          throw new Error('Metal is not available for transfer');
        }
        break;
    }

    if (!materialExists) {
      throw new Error('Material not found');
    }

    if (currentLocation !== request.from_location) {
      throw new Error('Material is not currently at the specified from location');
    }

    // Validate sufficient quantity/weight
    if (request.quantity && request.quantity > availableQuantity) {
      throw new Error('Insufficient quantity available for transfer');
    }

    if (request.weight_grams && request.weight_grams > availableQuantity) {
      throw new Error('Insufficient weight available for transfer');
    }

  } catch (error) {
    console.error('Material availability validation failed:', error);
    throw error;
  }
}

/**
 * Update material location after transfer
 */
async function updateMaterialLocation(
  materialType: string,
  materialId: string,
  newLocation: string
): Promise<void> {
  try {
    const updateData = {
      current_location: newLocation,
      updated_at: new Date().toISOString()
    };

    let error;

    switch (materialType) {
      case 'stone':
        ({ error } = await supabase
          .from('stone_inventory')
          .update(updateData)
          .eq('stone_inventory_id', materialId));
        break;

      case 'finding':
        ({ error } = await supabase
          .from('findings_mast')
          .update(updateData)
          .eq('finding_id', materialId));
        break;

      case 'metal':
        ({ error } = await supabase
          .from('metal_inventory')
          .update(updateData)
          .eq('metal_inventory_id', materialId));
        break;
    }

    if (error) throw error;

  } catch (error) {
    console.error('Error updating material location:', error);
    throw error;
  }
}

/**
 * Validate locations exist
 */
async function validateLocations(locationIds: string[]): Promise<void> {
  try {
    const { data: locations, error } = await supabase
      .from('inventory_locations')
      .select('location_id')
      .in('location_id', locationIds);

    if (error) throw error;

    if (!locations || locations.length !== locationIds.length) {
      throw new Error('One or more locations do not exist');
    }

  } catch (error) {
    console.error('Location validation failed:', error);
    throw error;
  }
}

/**
 * Get all inventory locations
 */
export async function getAllLocations(): Promise<InventoryLocation[]> {
  try {
    const { data, error } = await supabase
      .from('inventory_locations')
      .select('*')
      .eq('is_active', true)
      .order('location_name', { ascending: true });

    if (error) throw error;
    return data || [];

  } catch (error) {
    console.error('Error fetching locations:', error);
    throw error;
  }
}

/**
 * Get location transfer history
 */
export async function getLocationTransferHistory(
  materialId: string,
  materialType: string
): Promise<any[]> {
  try {
    const { data, error } = await supabase
      .from('material_location_transfers')
      .select(`
        *,
        from_location_details:inventory_locations!from_location(location_name, location_type),
        to_location_details:inventory_locations!to_location(location_name, location_type),
        transferred_by_user:users(username)
      `)
      .eq('material_id', materialId)
      .eq('material_type', materialType)
      .order('transfer_date', { ascending: false });

    if (error) throw error;
    return data || [];

  } catch (error) {
    console.error('Error fetching transfer history:', error);
    throw error;
  }
}

/**
 * Get location summary report
 */
export async function getLocationSummaryReport(): Promise<{
  locations: any[];
  totalsByLocation: Record<string, any>;
}> {
  try {
    const locations = await getAllLocations();
    const totalsByLocation: Record<string, any> = {};

    for (const location of locations) {
      const inventory = await getInventoryByLocation({
        location_id: location.location_id
      });

      totalsByLocation[location.location_id] = {
        location_name: location.location_name,
        location_type: location.location_type,
        stone_count: inventory.stones.length,
        finding_count: inventory.findings.length,
        metal_count: inventory.metals.length,
        total_stone_weight_carats: inventory.stones.reduce((sum: number, stone: any) => sum + (stone.weight_carats || 0), 0),
        total_finding_weight_grams: inventory.findings.reduce((sum: number, finding: any) => sum + (finding.gross_weight_grams || 0), 0),
        total_metal_weight_grams: inventory.metals.reduce((sum: number, metal: any) => sum + (metal.weight_grams || 0), 0)
      };
    }

    return {
      locations,
      totalsByLocation
    };

  } catch (error) {
    console.error('Error generating location summary report:', error);
    throw error;
  }
}
