/**
 * @component ProcessTransitionForm
 * @description Handles complex process transitions with multi-material additions
 * 
 * BUSINESS SCENARIO:
 * Filing → Setting: 9.7g gold + 1 carat diamonds + 0.45 carat polki → 9.9g gross, 9.7g net
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  calculateWeightAnalysis, 
  caratsToGrams, 
  gramsToCarats,
  WeightCalculationInput,
  WeightCalculationResult,
  PolkiAddition
} from '@/services/weightCalculationService';
import { 
  ScaleIcon,
  DiamondIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  PlusIcon,
  MinusIcon
} from 'lucide-react';

interface ProcessTransitionFormProps {
  orderId: string;
  fromProcess: string;
  toProcess: string;
  workerId: string;
  previousNetWeight: number;
  previousGrossWeight: number;
  onSubmit: (result: WeightCalculationResult) => void;
}

interface MaterialAddition {
  type: 'diamond' | 'polki' | 'finding';
  id: string;
  description: string;
  carats?: number;
  grams?: number;
  polkiDetails?: PolkiAddition;
}

export function ProcessTransitionForm({
  orderId,
  fromProcess,
  toProcess,
  workerId,
  previousNetWeight,
  previousGrossWeight,
  onSubmit
}: ProcessTransitionFormProps) {
  // Form state
  const [currentGrossWeight, setCurrentGrossWeight] = useState(previousGrossWeight);
  const [currentNetWeight, setCurrentNetWeight] = useState(previousNetWeight);
  const [dustCollected, setDustCollected] = useState(0);
  const [dustRecoveryPercentage, setDustRecoveryPercentage] = useState(70);
  
  // Material additions
  const [materialsAdded, setMaterialsAdded] = useState<MaterialAddition[]>([]);
  const [calculation, setCalculation] = useState<WeightCalculationResult | null>(null);

  // Calculate weights in real-time
  useEffect(() => {
    const diamondsAdded = materialsAdded
      .filter(m => m.type === 'diamond')
      .reduce((sum, m) => sum + (m.carats || 0), 0);
    
    const polkisAdded = materialsAdded
      .filter(m => m.type === 'polki' && m.polkiDetails)
      .map(m => m.polkiDetails!);

    const input: WeightCalculationInput = {
      previousNetWeight,
      previousGrossWeight,
      diamondsAdded,
      polkisAdded,
      currentGrossWeight,
      currentNetWeight,
      dustCollected,
      dustRecoveryPercentage,
      processType: toProcess,
      orderId,
      workerId
    };

    const result = calculateWeightAnalysis(input);
    setCalculation(result);
  }, [
    previousNetWeight, 
    previousGrossWeight, 
    currentGrossWeight, 
    currentNetWeight, 
    dustCollected, 
    dustRecoveryPercentage, 
    materialsAdded,
    toProcess,
    orderId,
    workerId
  ]);

  const addMaterial = (type: 'diamond' | 'polki' | 'finding') => {
    const newMaterial: MaterialAddition = {
      type,
      id: `${type}-${Date.now()}`,
      description: '',
      carats: type === 'diamond' ? 0 : undefined,
      grams: type === 'finding' ? 0 : undefined,
      polkiDetails: type === 'polki' ? {
        polkiId: `polki-${Date.now()}`,
        totalCarats: 0,
        diamondCarats: 0,
        silverCarats: 0
      } : undefined
    };
    setMaterialsAdded(prev => [...prev, newMaterial]);
  };

  const updateMaterial = (id: string, updates: Partial<MaterialAddition>) => {
    setMaterialsAdded(prev => 
      prev.map(m => m.id === id ? { ...m, ...updates } : m)
    );
  };

  const removeMaterial = (id: string) => {
    setMaterialsAdded(prev => prev.filter(m => m.id !== id));
  };

  const getStatusColor = (isValid: boolean, hasErrors: boolean) => {
    if (hasErrors) return 'text-red-600 bg-red-50';
    if (isValid) return 'text-green-600 bg-green-50';
    return 'text-yellow-600 bg-yellow-50';
  };

  const totalAddedWeight = materialsAdded.reduce((sum, m) => {
    if (m.type === 'diamond') return sum + caratsToGrams(m.carats || 0);
    if (m.type === 'polki' && m.polkiDetails) return sum + caratsToGrams(m.polkiDetails.totalCarats);
    if (m.type === 'finding') return sum + (m.grams || 0);
    return sum;
  }, 0);

  const expectedGrossWeight = previousNetWeight + totalAddedWeight;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRightIcon className="w-5 h-5" />
            Process Transition: {fromProcess} → {toProcess}
          </CardTitle>
          <CardDescription>
            Order {orderId} | Worker {workerId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium">Previous Weights</Label>
              <div className="space-y-1">
                <div>Net: {previousNetWeight.toFixed(3)}g</div>
                <div>Gross: {previousGrossWeight.toFixed(3)}g</div>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium">Expected After Addition</Label>
              <div className="space-y-1">
                <div>Net: {previousNetWeight.toFixed(3)}g (unchanged)</div>
                <div>Gross: {expectedGrossWeight.toFixed(3)}g (+{totalAddedWeight.toFixed(3)}g)</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Material Additions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PlusIcon className="w-5 h-5" />
            Materials Added in {toProcess}
          </CardTitle>
          <CardDescription>
            Add diamonds, polkis, and findings used in this process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Add Material Buttons */}
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => addMaterial('diamond')}
              >
                <DiamondIcon className="w-4 h-4 mr-2" />
                Add Diamond
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => addMaterial('polki')}
              >
                <ScaleIcon className="w-4 h-4 mr-2" />
                Add Polki
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => addMaterial('finding')}
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Add Finding
              </Button>
            </div>

            {/* Material List */}
            {materialsAdded.map((material) => (
              <Card key={material.id} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className="capitalize">
                    {material.type}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMaterial(material.id)}
                  >
                    <MinusIcon className="w-4 h-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor={`desc-${material.id}`}>Description</Label>
                    <Input
                      id={`desc-${material.id}`}
                      value={material.description}
                      onChange={(e) => updateMaterial(material.id, { description: e.target.value })}
                      placeholder={`${material.type} description`}
                    />
                  </div>

                  {material.type === 'diamond' && (
                    <div>
                      <Label htmlFor={`carats-${material.id}`}>Carats</Label>
                      <Input
                        id={`carats-${material.id}`}
                        type="number"
                        step="0.01"
                        value={material.carats || 0}
                        onChange={(e) => updateMaterial(material.id, { carats: parseFloat(e.target.value) || 0 })}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        = {caratsToGrams(material.carats || 0).toFixed(3)}g
                      </div>
                    </div>
                  )}

                  {material.type === 'polki' && material.polkiDetails && (
                    <div className="space-y-2">
                      <div>
                        <Label>Total Carats</Label>
                        <Input
                          type="number"
                          step="0.01"
                          value={material.polkiDetails.totalCarats}
                          onChange={(e) => updateMaterial(material.id, {
                            polkiDetails: {
                              ...material.polkiDetails!,
                              totalCarats: parseFloat(e.target.value) || 0
                            }
                          })}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <Label className="text-xs">Diamond Carats</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={material.polkiDetails.diamondCarats}
                            onChange={(e) => updateMaterial(material.id, {
                              polkiDetails: {
                                ...material.polkiDetails!,
                                diamondCarats: parseFloat(e.target.value) || 0,
                                silverCarats: material.polkiDetails!.totalCarats - (parseFloat(e.target.value) || 0)
                              }
                            })}
                          />
                        </div>
                        <div>
                          <Label className="text-xs">Silver Carats</Label>
                          <Input
                            type="number"
                            step="0.01"
                            value={material.polkiDetails.silverCarats}
                            readOnly
                            className="bg-gray-50"
                          />
                        </div>
                      </div>
                      <div className="text-xs text-gray-500">
                        = {caratsToGrams(material.polkiDetails.totalCarats).toFixed(3)}g total
                      </div>
                    </div>
                  )}

                  {material.type === 'finding' && (
                    <div>
                      <Label htmlFor={`grams-${material.id}`}>Weight (grams)</Label>
                      <Input
                        id={`grams-${material.id}`}
                        type="number"
                        step="0.001"
                        value={material.grams || 0}
                        onChange={(e) => updateMaterial(material.id, { grams: parseFloat(e.target.value) || 0 })}
                      />
                    </div>
                  )}
                </div>
              </Card>
            ))}

            {materialsAdded.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No materials added yet. Click the buttons above to add materials.
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Output Weights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ScaleIcon className="w-5 h-5" />
            Process Output Weights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="current-gross">Current Gross Weight (g)</Label>
              <Input
                id="current-gross"
                type="number"
                step="0.001"
                value={currentGrossWeight}
                onChange={(e) => setCurrentGrossWeight(parseFloat(e.target.value) || 0)}
              />
              <div className="text-xs text-gray-500 mt-1">
                Expected: {expectedGrossWeight.toFixed(3)}g
              </div>
            </div>
            <div>
              <Label htmlFor="current-net">Current Net Weight (g)</Label>
              <Input
                id="current-net"
                type="number"
                step="0.001"
                value={currentNetWeight}
                onChange={(e) => setCurrentNetWeight(parseFloat(e.target.value) || 0)}
              />
              <div className="text-xs text-gray-500 mt-1">
                Expected: {previousNetWeight.toFixed(3)}g (no change)
              </div>
            </div>
          </div>

          {toProcess.toLowerCase().includes('filing') && (
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <Label htmlFor="dust-collected">Dust Collected (g)</Label>
                <Input
                  id="dust-collected"
                  type="number"
                  step="0.001"
                  value={dustCollected}
                  onChange={(e) => setDustCollected(parseFloat(e.target.value) || 0)}
                />
              </div>
              <div>
                <Label htmlFor="dust-recovery">Dust Recovery %</Label>
                <Input
                  id="dust-recovery"
                  type="number"
                  step="1"
                  value={dustRecoveryPercentage}
                  onChange={(e) => setDustRecoveryPercentage(parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Calculation Results */}
      {calculation && (
        <Card className={getStatusColor(calculation.isValid, calculation.validationErrors.length > 0)}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {calculation.isValid ? (
                <CheckCircleIcon className="w-5 h-5 text-green-600" />
              ) : (
                <AlertTriangleIcon className="w-5 h-5 text-red-600" />
              )}
              Weight Analysis Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <Label className="font-medium">Metal Loss</Label>
                <div>{calculation.metalLossGrams.toFixed(3)}g ({calculation.metalLossPercentage.toFixed(2)}%)</div>
              </div>
              <div>
                <Label className="font-medium">Materials Added</Label>
                <div>
                  {calculation.totalDiamondsCarats > 0 && (
                    <div>Diamonds: {calculation.totalDiamondsCarats.toFixed(2)} carats</div>
                  )}
                  {calculation.totalPolkisCarats > 0 && (
                    <div>Polkis: {calculation.totalPolkisCarats.toFixed(2)} carats</div>
                  )}
                </div>
              </div>
            </div>

            {calculation.validationErrors.length > 0 && (
              <Alert className="mb-4">
                <AlertTriangleIcon className="w-4 h-4" />
                <AlertDescription>
                  <ul className="list-disc list-inside">
                    {calculation.validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {calculation.recommendations.length > 0 && (
              <div>
                <Label className="font-medium">Recommendations</Label>
                <ul className="list-disc list-inside text-sm">
                  {calculation.recommendations.map((rec, index) => (
                    <li key={index}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Submit */}
      <div className="flex justify-end">
        <Button 
          onClick={() => calculation && onSubmit(calculation)}
          disabled={!calculation || !calculation.isValid}
          className="w-full"
        >
          Complete Process Transition
        </Button>
      </div>
    </div>
  );
}
