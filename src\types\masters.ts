/**
 * Master Data Type Definitions
 * Contains type definitions for all master data entities in the system
 * @module types/masters
 */

import { BaseEntity } from './common';
import { <PERSON>son } from './supabase';

/**
 * Base interface for all master data types
 * Provides common timestamp fields
 */
export interface BaseMaster {
  /** Creation timestamp */
  created_at: string | null;
  /** Last update timestamp */
  updated_at: string | null;
}

/**
 * Item Type Master
 * Defines different types of jewelry items that can be manufactured
 */
export interface ItemTypeMaster extends BaseEntity {
  /** Unique identifier for the item type */
  item_type_id: string;
  /** Description of the item type (e.g., "Ring", "Necklace") */
  description: string;
  /** Average time required to process this type of item (in minutes) */
  average_processing_time: number;
  /** Short code used in order numbers (e.g., "RG" for Ring) */
  suffix: string;
}

/**
 * Karat Master
 * Defines different gold purity levels and their properties
 */
export interface KaratMaster extends BaseEntity {
  /** Unique identifier for the karat type */
  karat_id: string;
  /** Description of the karat (e.g., "22K", "18K") */
  description: string;
  /** Gold purity percentage */
  purity: number;
  /** Standard wastage percentage for this purity */
  standard_wastage: number;
  /** Density of the metal at this purity */
  density: number;
}

/**
 * Metal Color Master
 * Defines different metal colors for all metal types and their processing requirements
 */
export interface MetalColorMaster extends BaseEntity {
  /** Unique identifier for the metal color */
  metal_colour_id: string;
  /** Reference to the metal type (Gold, Silver, Platinum, etc.) */
  metal_type_id: string;
  /** Description of the color (e.g., "Yellow", "Rose" for Gold; "Natural", "Oxidized" for Silver) */
  description: string;
  /** Factor affecting processing time based on color complexity */
  processing_complexity_factor: number;
  /** Whether this color is active */
  is_active: boolean;
  /** Related metal type information */
  metal_type?: {
    name: string;
    description?: string;
  };
}

/**
 * Purity Master (replaces KaratMaster)
 * Defines metal purities for all metal types
 */
export interface PurityMaster extends BaseEntity {
  /** Unique identifier for the purity */
  purity_id: string;
  /** Reference to the metal type (Gold, Silver, Platinum, etc.) */
  metal_type_id: string;
  /** Description of the purity (e.g., "22KT", "18KT" for Gold; "925 Sterling" for Silver) */
  description: string;
  /** Actual purity percentage (e.g., 91.67 for 22KT Gold, 92.5 for Sterling Silver) */
  purity_percentage: number;
  /** Standard wastage percentage for this purity */
  standard_wastage_percentage: number;
  /** Density of the metal at this purity */
  density: number;
  /** Whether this purity is active */
  is_active: boolean;
  /** Related metal type information */
  metal_type?: {
    name: string;
    description?: string;
  };
}

/**
 * Metal Type Master
 * Defines different types of metals (Gold, Silver, Platinum, etc.)
 */
export interface MetalTypeMaster extends BaseEntity {
  /** Unique identifier for the metal type */
  metal_type_id: string;
  /** Name of the metal (e.g., "Gold", "Silver", "Platinum") */
  name: string;
  /** Description of the metal */
  description?: string;
  /** Whether this metal type is active */
  is_active: boolean;
}

/**
 * Legacy Karat Master (use PurityMaster for new implementations)
 * Defines different gold karats and their properties
 * @deprecated Use PurityMaster instead for multi-metal support
 */
export interface KaratMaster extends BaseEntity {
  /** Unique identifier for the karat */
  karat_id: string;
  /** Description of the karat (e.g., "22KT", "18KT") */
  description: string;
  /** Purity percentage of the gold */
  purity: number;
  /** Standard wastage percentage for this karat */
  standard_wastage: number;
  /** Density of the gold at this karat */
  density: number;
}

/**
 * Legacy Gold Color Master (use MetalColorMaster for new implementations)
 * @deprecated Use MetalColorMaster instead for multi-metal support
 */
export interface GoldColorMaster extends BaseEntity {
  /** Unique identifier for the gold color */
  gold_colour_id: string;
  /** Description of the color (e.g., "Yellow", "Rose") */
  description: string;
  /** Factor affecting processing time based on color complexity */
  processing_complexity_factor: number;
}

/**
 * Order Category Master
 * Defines different categories of orders with their processing requirements
 */
export interface OrderCategoryMaster extends BaseEntity {
  /** Unique identifier for the order category */
  order_category_id: string;
  /** Description of the category */
  description: string;
  /** Base processing time for orders in this category (in minutes) */
  base_processing_time: number;
}

/**
 * Style Master
 * Defines jewelry styles with their specifications and processing requirements
 */
export interface StyleMaster extends BaseEntity {
  /** Unique identifier for the style */
  style_id: string;
  /** Reference to the item type */
  item_type_id: string | null;
  /** Reference to the party/customer if style is customer-specific */
  party_id: string | null;
  /** Net weight of the style in grams */
  net_wt: number;
  /** Karat specification for the style */
  net_wt_kt: string | null;
  /** Estimated time to process this style (in minutes) */
  estimated_processing_time: number;
  /** Additional notes for processing this style */
  processing_notes: string | null;

  // New style code management fields
  /** Style code for identification (can be same as order number for new styles) */
  style_code?: string;
  /** Whether this is a repeat/variation of an existing style */
  is_repeat_style: boolean;
  /** Reference to parent style if this is a variation */
  parent_style_id?: string;
  /** Complexity level (1-5) for processing time calculation */
  complexity_level?: number;
  /** Design-specific notes and requirements */
  design_notes?: string;
  /** Whether CAD work is required for this style */
  cad_required: boolean;
  /** Whether CAM work is required for this style */
  cam_required: boolean;

  // Relations
  parent_style?: Pick<StyleMaster, 'style_id' | 'style_code'>;
}

/**
 * Customer Master
 * Defines regular customers in the system
 */
export interface CustomerMaster extends BaseMaster {
  /** Unique identifier for the customer */
  customer_id: string;
  /** Customer name or description */
  description: string;
}

/**
 * Third Party Customer Master
 * Defines external business partners or vendors
 */
export interface ThirdPartyCustomerMaster extends BaseMaster {
  /** Unique identifier for the third party */
  party_cust_id: string;
  /** Third party name or description */
  description: string;
}

/**
 * Worker Master
 * Defines workers and their capabilities
 */
export interface WorkerMaster extends BaseEntity {
  /** Unique identifier for the worker */
  worker_id: string;
  /** Worker's name */
  name: string;
  /** JSON object containing worker's skills */
  skills?: Json;
  /** Worker's efficiency factor (1.0 = standard) */
  efficiency_factor: number;
  /** JSON object containing worker's working hours */
  working_hours?: Json;
  /** Whether the worker is currently active */
  is_active: boolean;
}

/**
 * Process Master
 * Defines manufacturing processes and their requirements
 */
export interface ProcessMaster extends BaseEntity {
  /** Unique identifier for the process */
  process_id: string;
  /** Description of the process */
  description: string;
  /** Standard time to complete this process (in minutes) */
  standard_time: number;
  /** JSON object containing required skills for this process */
  required_skills?: Json;
  /** Order in which this process should be performed */
  sequence_number: number;
  /** Whether this process can be skipped */
  is_optional: boolean;
}

/**
 * Worker Skill Matrix
 * Maps workers to processes with their skill levels
 */
export interface WorkerSkillMatrix extends BaseEntity {
  /** Reference to the worker */
  worker_id: string;
  /** Reference to the process */
  process_id: string;
  /** Worker's skill level for this process (1-5) */
  skill_level: number;
  /** Worker's efficiency factor for this specific process */
  efficiency_factor: number;
  /** Date of last training for this process */
  last_training_date?: string;
}

/**
 * Resource Capacity
 * Tracks process capacity and allocation for scheduling
 */
export interface ResourceCapacity extends BaseEntity {
  /** Reference to the process */
  process_id: string;
  /** Date for which capacity is defined */
  work_date: string;
  /** Total available minutes for this process */
  total_minutes_available: number;
  /** Minutes already allocated to orders */
  allocated_minutes: number;
  /** Buffer time reserved for unexpected work */
  buffer_minutes: number;
}
