/**
 * @module components/materials/DustCollectionDialog
 * @description Dialog for collecting dust during material receipt process
 * 
 * Features:
 * - Individual dust collection per material item
 * - Batch dust collection for multiple items
 * - Process-specific dust type detection
 * - Automatic recovery rate estimation
 * - Integration with dust parcel system
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  FlaskConicalIcon, 
  ScaleIcon, 
  AlertTriangleIcon,
  CheckCircleIcon,
  InfoIcon
} from 'lucide-react';
import { createDustParcel } from '@/services/dustManagementService';

const dustCollectionSchema = z.object({
  items: z.array(z.object({
    item_id: z.string(),
    dust_weight_grams: z.number().min(0, 'Dust weight must be positive'),
    dust_type: z.enum(['filing', 'setting', 'polish', 'bob', 'mixed']),
    notes: z.string().optional()
  })),
  batch_notes: z.string().optional()
});

export interface MaterialItem {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  issuedWeight: number;
  receivedWeight: number;
  dustCollected: number;
  lossPercentage: number;
  status: 'pending' | 'completed' | 'flagged';
  originalData: any;
}

interface DustCollectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItems: MaterialItem[];
  workerId: string;
  processId: string;
  customerId: string;
  onDustCollected: (itemId: string, dustWeight: number) => void;
}

interface DustCollectionFormData {
  items: {
    item_id: string;
    dust_weight_grams: number;
    dust_type: 'filing' | 'setting' | 'polish' | 'bob' | 'mixed';
    notes?: string;
  }[];
  batch_notes?: string;
}

// Process-specific dust type mapping
const PROCESS_DUST_TYPE_MAP: Record<string, 'filing' | 'setting' | 'polish' | 'bob' | 'mixed'> = {
  'filing': 'filing',
  'setting': 'setting',
  'polishing': 'polish',
  'polish': 'polish',
  'bob': 'bob',
  'default': 'mixed'
};

// Recovery rate estimates by dust type
const RECOVERY_RATES = {
  filing: 85,
  setting: 80,
  polish: 75,
  bob: 70,
  mixed: 75
};

export function DustCollectionDialog({
  isOpen,
  onClose,
  selectedItems,
  workerId,
  processId,
  customerId,
  onDustCollected
}: DustCollectionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [processName, setProcessName] = useState<string>('');

  const form = useForm<DustCollectionFormData>({
    resolver: zodResolver(dustCollectionSchema),
    defaultValues: {
      items: selectedItems.map(item => ({
        item_id: item.id,
        dust_weight_grams: 0,
        dust_type: 'mixed',
        notes: ''
      })),
      batch_notes: ''
    }
  });

  // Load process information to determine dust type
  useEffect(() => {
    if (processId) {
      // This would typically fetch from API, but for now we'll use a simple mapping
      const processNames: Record<string, string> = {
        'filing': 'Filing',
        'setting': 'Stone Setting',
        'polishing': 'Polishing',
        'bob': 'Bob Work'
      };
      setProcessName(processNames[processId] || 'Unknown Process');
      
      // Auto-detect dust type based on process
      const dustType = PROCESS_DUST_TYPE_MAP[processId] || 'mixed';
      const currentItems = form.getValues('items');
      const updatedItems = currentItems.map(item => ({
        ...item,
        dust_type: dustType
      }));
      form.setValue('items', updatedItems);
    }
  }, [processId, form]);

  const onSubmit = async (data: DustCollectionFormData) => {
    setIsSubmitting(true);
    try {
      // Create dust parcels for each item with dust
      const dustPromises = data.items
        .filter(item => item.dust_weight_grams > 0)
        .map(async (item) => {
          const materialItem = selectedItems.find(mi => mi.id === item.item_id);
          if (!materialItem) return;

          // Create dust parcel
          await createDustParcel({
            transaction_id: item.item_id,
            worker_id: workerId,
            process_id: processId,
            customer_id: customerId,
            weight_grams: item.dust_weight_grams,
            purity_estimate: RECOVERY_RATES[item.dust_type],
            collection_notes: item.notes || data.batch_notes
          });

          // Update the material item
          onDustCollected(item.item_id, item.dust_weight_grams);
        });

      await Promise.all(dustPromises);

      const totalDust = data.items.reduce((sum, item) => sum + item.dust_weight_grams, 0);
      const itemsWithDust = data.items.filter(item => item.dust_weight_grams > 0).length;

      toast.success(
        `Dust collection completed: ${totalDust.toFixed(2)}g collected from ${itemsWithDust} items`
      );
      
      onClose();
    } catch (error) {
      console.error('Error collecting dust:', error);
      toast.error('Failed to collect dust');
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalDustWeight = form.watch('items')?.reduce((sum, item) => sum + (item.dust_weight_grams || 0), 0) || 0;
  const estimatedRecovery = form.watch('items')?.reduce((sum, item) => {
    const recoveryRate = RECOVERY_RATES[item.dust_type] / 100;
    return sum + (item.dust_weight_grams || 0) * recoveryRate;
  }, 0) || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FlaskConicalIcon className="w-5 h-5" />
            Dust Collection - {processName}
          </DialogTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Collect dust generated during the {processName.toLowerCase()} process for {selectedItems.length} material items.
          </p>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-3 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <ScaleIcon className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Total Dust</span>
              </div>
              <div className="text-2xl font-bold text-blue-900">{totalDustWeight.toFixed(3)}g</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircleIcon className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Est. Recovery</span>
              </div>
              <div className="text-2xl font-bold text-green-900">{estimatedRecovery.toFixed(3)}g</div>
            </div>
            <div className="bg-amber-50 p-4 rounded-lg">
              <div className="flex items-center gap-2">
                <InfoIcon className="w-4 h-4 text-amber-600" />
                <span className="text-sm font-medium text-amber-800">Items</span>
              </div>
              <div className="text-2xl font-bold text-amber-900">{selectedItems.length}</div>
            </div>
          </div>

          {/* Dust Collection Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Material</TableHead>
                  <TableHead>Issued Weight</TableHead>
                  <TableHead>Dust Weight (g)</TableHead>
                  <TableHead>Dust Type</TableHead>
                  <TableHead>Recovery %</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {selectedItems.map((item, index) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.description}</div>
                        <Badge variant="outline" className="text-xs">
                          {item.type.toUpperCase()}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>{item.issuedWeight.toFixed(3)}g</TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        step="0.001"
                        min="0"
                        placeholder="0.000"
                        {...form.register(`items.${index}.dust_weight_grams`, { valueAsNumber: true })}
                        className="w-24"
                      />
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {form.watch(`items.${index}.dust_type`)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm font-medium">
                        {RECOVERY_RATES[form.watch(`items.${index}.dust_type`) || 'mixed']}%
                      </span>
                    </TableCell>
                    <TableCell>
                      <Input
                        placeholder="Optional notes"
                        {...form.register(`items.${index}.notes`)}
                        className="w-32"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Batch Notes */}
          <div>
            <Label htmlFor="batch_notes">Batch Notes (Optional)</Label>
            <Textarea
              id="batch_notes"
              placeholder="Add any additional notes for this dust collection batch..."
              {...form.register('batch_notes')}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || totalDustWeight === 0}>
              {isSubmitting ? 'Collecting...' : `Collect ${totalDustWeight.toFixed(3)}g Dust`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
