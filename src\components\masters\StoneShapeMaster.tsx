/**
 * StoneShapeMaster Component
 * Manages the stone shape master data for inventory management
 * 
 * @module components/masters
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Pencil, Trash2 } from 'lucide-react';
import { StoneShapeMast } from '@/types/inventory';
import { stoneShapeMastFields } from '@/components/masters/forms/FormFields';
import { MasterForm } from '@/components/common/Form/MasterForm';
import { FormField } from '@/types/common';
import { useToast } from '@/hooks/useToast';

/**
 * StoneShapeMaster Component
 * Allows management of stone shapes used in jewelry manufacturing
 */
export const StoneShapeMaster: React.FC = () => {
  const [stoneShapes, setStoneShapes] = useState<StoneShapeMast[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showForm, setShowForm] = useState<boolean>(false);
  const [selectedItem, setSelectedItem] = useState<StoneShapeMast | null>(null);
  const { showToast } = useToast();

  // Convert the record to array of FormField for MasterForm
  const formFields = Object.entries(stoneShapeMastFields).map(([id, field]) => ({
    ...field,
    id,
  }));

  const formConfig = {
    fields: formFields,
    title: 'Stone Shape',
  };

  useEffect(() => {
    fetchStoneShapes();
  }, []);

  /**
   * Fetches stone shapes from the database
   */
  const fetchStoneShapes = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/masters/stone-shapes');
      
      if (!response.ok) {
        throw new Error('Failed to fetch stone shapes');
      }
      
      const data = await response.json();
      setStoneShapes(data);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      console.error('Error fetching stone shapes:', err);
      showToast({
        title: 'Error',
        description: errorMessage || 'Error fetching stone shapes',
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles form submission for creating or updating a stone shape
   * @param values - The form values
   */
  const handleSubmit = async (values: Partial<StoneShapeMast>) => {
    try {
      const url = '/api/masters/stone-shapes';
      const method = selectedItem ? 'PUT' : 'POST';
      const body = selectedItem 
        ? JSON.stringify({ ...values, shape_id: selectedItem.shape_id }) 
        : JSON.stringify(values);
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      });
      
      if (!response.ok) {
        throw new Error('Failed to save stone shape');
      }
      
      await fetchStoneShapes();
      setShowForm(false);
      setSelectedItem(null);
      showToast({
        title: 'Success',
        description: `Stone shape ${selectedItem ? 'updated' : 'created'} successfully`,
        type: 'success'
      });
    } catch (err) {
      console.error('Error saving stone shape:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      showToast({
        title: 'Error',
        description: errorMessage || 'Error saving stone shape',
        type: 'destructive'
      });
    }
  };

  /**
   * Handles deleting a stone shape
   * @param item - The stone shape to delete
   */
  const handleDelete = async (item: StoneShapeMast) => {
    if (!confirm(`Are you sure you want to delete ${item.name}?`)) {
      return;
    }
    
    try {
      const response = await fetch(`/api/masters/stone-shapes?id=${item.shape_id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete stone shape');
      }
      
      await fetchStoneShapes();
      showToast({
        title: 'Success',
        description: 'Stone shape deleted successfully',
        type: 'success'
      });
    } catch (err) {
      console.error('Error deleting stone shape:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      showToast({
        title: 'Error',
        description: errorMessage || 'Error deleting stone shape',
        type: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Stone Shapes</h1>
        <button
          onClick={() => {
            setSelectedItem(null);
            setShowForm(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-5 h-5 mr-2" />
          Add Stone Shape
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {stoneShapes.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No stone shapes found
                  </td>
                </tr>
              ) : (
                stoneShapes.map((item) => (
                  <tr key={item.shape_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {item.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {item.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          item.is_active
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        }`}
                      >
                        {item.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedItem(item);
                          setShowForm(true);
                        }}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                      >
                        <Pencil className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(item)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}

      {showForm && (
        <MasterForm
          config={formConfig}
          initialData={selectedItem}
          onSubmit={handleSubmit}
          onCancel={() => {
            setShowForm(false);
            setSelectedItem(null);
          }}
        />
      )}
    </div>
  );
};
