-- =====================================================
-- SETTING PROCESS SYNCHRONIZATION MIGRATION
-- Date: 2025-07-03
-- Purpose: Fix database-frontend synchronization issues
-- =====================================================

-- 1. Add missing columns to stone_transaction_details for Setting Process
ALTER TABLE public.stone_transaction_details 
ADD COLUMN IF NOT EXISTS carat_weight_consumed numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS carat_weight_damaged numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS carat_weight_lost numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS disposition_notes text;

-- 2. Add missing columns to finding_transaction_details for Setting Process  
ALTER TABLE public.finding_transaction_details
ADD COLUMN IF NOT EXISTS quantity_consumed integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS quantity_damaged integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS quantity_lost integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS weight_consumed_grams numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS weight_damaged_grams numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS weight_lost_grams numeric(10,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS condition_status varchar DEFAULT 'good' CHECK (condition_status IN ('good', 'damaged', 'lost')),
ADD COLUMN IF NOT EXISTS disposition_notes text;

-- 3. Add material_transactions columns that services expect
ALTER TABLE public.material_transactions
ADD COLUMN IF NOT EXISTS customer_id uuid REFERENCES public.customer_mast(customer_id),
ADD COLUMN IF NOT EXISTS material_type varchar CHECK (material_type IN ('stone', 'finding', 'metal')),
ADD COLUMN IF NOT EXISTS status varchar DEFAULT 'issued' CHECK (status IN ('issued', 'in_process', 'completed', 'cancelled')),
ADD COLUMN IF NOT EXISTS issued_by uuid REFERENCES public.user_roles(user_id),
ADD COLUMN IF NOT EXISTS received_by uuid REFERENCES public.user_roles(user_id),
ADD COLUMN IF NOT EXISTS issued_weight_grams numeric(10,4),
ADD COLUMN IF NOT EXISTS received_weight_grams numeric(10,4),
ADD COLUMN IF NOT EXISTS dust_collected_grams numeric(10,4),
ADD COLUMN IF NOT EXISTS loss_percentage numeric(5,2),
ADD COLUMN IF NOT EXISTS expected_loss_percentage numeric(5,2),
ADD COLUMN IF NOT EXISTS received_date timestamptz,
ADD COLUMN IF NOT EXISTS gross_weight_after numeric(10,4),
ADD COLUMN IF NOT EXISTS net_weight_after numeric(10,4);

-- 4. Create setting_process_receipts table for comprehensive setting tracking
CREATE TABLE IF NOT EXISTS public.setting_process_receipts (
  receipt_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id uuid NOT NULL REFERENCES public.material_transactions(transaction_id),
  order_id uuid NOT NULL REFERENCES public.orders(order_id),
  worker_id uuid NOT NULL REFERENCES public.worker_mast(worker_id),
  process_id uuid NOT NULL REFERENCES public.process_mast(process_id),
  
  -- Setting scenario
  scenario varchar NOT NULL CHECK (scenario IN ('all_set', 'all_returned', 'mixed')),
  
  -- Product weights
  gross_weight_received numeric(10,4) NOT NULL,
  net_weight_received numeric(10,4) NOT NULL,
  
  -- Summary counts
  total_stones_set integer DEFAULT 0,
  total_stones_returned integer DEFAULT 0,
  total_stones_broken integer DEFAULT 0,
  total_stones_lost integer DEFAULT 0,
  
  -- Summary weights
  total_carats_set numeric(10,4) DEFAULT 0,
  total_carats_returned numeric(10,4) DEFAULT 0,
  total_carats_broken numeric(10,4) DEFAULT 0,
  total_carats_lost numeric(10,4) DEFAULT 0,
  
  -- Metadata
  notes text,
  created_by uuid REFERENCES public.user_roles(user_id),
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamptz DEFAULT CURRENT_TIMESTAMP
);

-- 5. Create stone_disposition_details for detailed stone tracking
CREATE TABLE IF NOT EXISTS public.stone_disposition_details (
  disposition_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_id uuid NOT NULL REFERENCES public.setting_process_receipts(receipt_id) ON DELETE CASCADE,
  stone_transaction_detail_id uuid NOT NULL REFERENCES public.stone_transaction_details(detail_id),
  
  -- Stone identification
  stone_type_id uuid NOT NULL REFERENCES public.stone_type_mast(stone_type_id),
  stone_shape_id uuid NOT NULL REFERENCES public.stone_shape_mast(shape_id),
  stone_size_id uuid NOT NULL REFERENCES public.stone_size_mast(size_id),
  description text NOT NULL,
  
  -- Quantities issued
  quantity_issued integer NOT NULL,
  carats_issued numeric(10,4) NOT NULL,
  
  -- Disposition breakdown
  quantity_set integer DEFAULT 0,
  quantity_returned integer DEFAULT 0,
  quantity_broken integer DEFAULT 0,
  quantity_lost integer DEFAULT 0,
  
  -- Carat breakdown
  carats_set numeric(10,4) DEFAULT 0,
  carats_returned numeric(10,4) DEFAULT 0,
  carats_broken numeric(10,4) DEFAULT 0,
  carats_lost numeric(10,4) DEFAULT 0,
  
  -- Notes
  disposition_notes text,
  created_at timestamptz DEFAULT CURRENT_TIMESTAMP,
  
  -- Validation constraint
  CONSTRAINT valid_disposition_quantities 
    CHECK (quantity_set + quantity_returned + quantity_broken + quantity_lost = quantity_issued),
  CONSTRAINT valid_disposition_carats 
    CHECK (ABS((carats_set + carats_returned + carats_broken + carats_lost) - carats_issued) < 0.01)
);

-- 6. Enable RLS on new tables
ALTER TABLE public.setting_process_receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stone_disposition_details ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies
CREATE POLICY "Allow authenticated users to manage setting receipts" ON public.setting_process_receipts
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Allow authenticated users to manage stone dispositions" ON public.stone_disposition_details
    FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- 8. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_setting_receipts_order_id ON public.setting_process_receipts(order_id);
CREATE INDEX IF NOT EXISTS idx_setting_receipts_worker_id ON public.setting_process_receipts(worker_id);
CREATE INDEX IF NOT EXISTS idx_setting_receipts_created_at ON public.setting_process_receipts(created_at);
CREATE INDEX IF NOT EXISTS idx_stone_disposition_receipt_id ON public.stone_disposition_details(receipt_id);
CREATE INDEX IF NOT EXISTS idx_material_transactions_status ON public.material_transactions(status);
CREATE INDEX IF NOT EXISTS idx_material_transactions_customer_id ON public.material_transactions(customer_id);

-- 9. Add comments for documentation
COMMENT ON TABLE public.setting_process_receipts IS 'Comprehensive setting process receipts with scenario tracking';
COMMENT ON TABLE public.stone_disposition_details IS 'Detailed stone disposition tracking for setting process';
COMMENT ON COLUMN public.stone_transaction_details.carat_weight_consumed IS 'Weight of stones that were set/consumed in carats';
COMMENT ON COLUMN public.stone_transaction_details.carat_weight_damaged IS 'Weight of stones that were broken/damaged in carats';
COMMENT ON COLUMN public.stone_transaction_details.carat_weight_lost IS 'Weight of stones that were lost in carats';

-- 10. Update existing data to match new schema (if any exists)
UPDATE public.material_transactions 
SET status = 'issued' 
WHERE status IS NULL;

-- 11. Create view for easy setting process reporting
CREATE OR REPLACE VIEW public.setting_process_summary AS
SELECT
  spr.receipt_id,
  spr.order_id,
  o.order_reference_no,
  COALESCE(c.customer_name, c.customer_code) as customer_name,
  w.name as worker_name,
  p.name as process_name,
  spr.scenario,
  spr.gross_weight_received,
  spr.net_weight_received,
  spr.total_stones_set,
  spr.total_stones_returned,
  spr.total_stones_broken,
  spr.total_stones_lost,
  spr.total_carats_set,
  spr.total_carats_returned,
  spr.total_carats_broken,
  spr.total_carats_lost,
  spr.created_at
FROM public.setting_process_receipts spr
JOIN public.orders o ON spr.order_id = o.order_id
JOIN public.customer_mast c ON o.customer_id = c.customer_id
JOIN public.worker_mast w ON spr.worker_id = w.worker_id
JOIN public.process_mast p ON spr.process_id = p.process_id
ORDER BY spr.created_at DESC;

COMMENT ON VIEW public.setting_process_summary IS 'Summary view for setting process receipts with related data';

-- 12. Grant permissions
GRANT SELECT ON public.setting_process_summary TO authenticated;
GRANT ALL ON public.setting_process_receipts TO authenticated;
GRANT ALL ON public.stone_disposition_details TO authenticated;

-- 13. Add trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_setting_process_receipts_updated_at 
    BEFORE UPDATE ON public.setting_process_receipts 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- 14. Create function for automatic stone disposition summary calculation
CREATE OR REPLACE FUNCTION public.calculate_setting_receipt_summary()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the setting_process_receipts summary when stone_disposition_details change
    UPDATE public.setting_process_receipts 
    SET 
        total_stones_set = (
            SELECT COALESCE(SUM(quantity_set), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_stones_returned = (
            SELECT COALESCE(SUM(quantity_returned), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_stones_broken = (
            SELECT COALESCE(SUM(quantity_broken), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_stones_lost = (
            SELECT COALESCE(SUM(quantity_lost), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_carats_set = (
            SELECT COALESCE(SUM(carats_set), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_carats_returned = (
            SELECT COALESCE(SUM(carats_returned), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_carats_broken = (
            SELECT COALESCE(SUM(carats_broken), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        total_carats_lost = (
            SELECT COALESCE(SUM(carats_lost), 0) 
            FROM public.stone_disposition_details 
            WHERE receipt_id = NEW.receipt_id
        ),
        updated_at = CURRENT_TIMESTAMP
    WHERE receipt_id = NEW.receipt_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_setting_receipt_summary 
    AFTER INSERT OR UPDATE OR DELETE ON public.stone_disposition_details 
    FOR EACH ROW EXECUTE FUNCTION public.calculate_setting_receipt_summary();

-- Migration completed successfully
SELECT 'Setting Process Synchronization Migration Completed Successfully' as status;
