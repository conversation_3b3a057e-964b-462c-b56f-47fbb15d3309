# Jwlprocmanage Project Rules and Guidelines

## 1. Database Architecture

### 1.1 No Database Triggers
- ✅ All business logic should be in application code, not in database triggers
- ✅ Exception: Keep core Supabase system triggers (storage, pgsodium, vault, realtime)
- ✅ Handle timestamps (created_at, updated_at) in application code

### 1.2 Database Operations
- ✅ All database operations must go through dedicated query classes
- ✅ Each entity should have its own query class
- ✅ Extend BaseQueries for common CRUD operations
- ✅ Use transactions for operations that modify multiple tables

### 1.3 Database Standards
- ✅ Use snake_case for all database identifiers
- ✅ Primary keys must be UUIDs
- ✅ Foreign keys must match referenced column names
- ✅ Status fields must use UPPERCASE values
- ✅ All tables must have created_at and updated_at timestamps
- ✅ Boolean fields should use is_ or has_ prefix

## 2. Code Organization

### 2.1 Directory Structure
```
src/
├── db/
│   ├── queries/           # Database query classes
│   │   ├── base.ts       # Base query class
│   │   └── [entity].ts   # Entity-specific queries
│   ├── types.ts          # Database types
│   └── utils.ts          # Database utilities
├── app/                  # Next.js pages
├── components/          # React components
├── hooks/              # Custom hooks
├── lib/               # Utilities and configurations
└── types/            # TypeScript type definitions
```

### 2.2 Code Standards
- ✅ Use TypeScript for all new code
- ✅ Follow ESLint and Prettier configurations
- ✅ Keep components under 300 lines
- ✅ Maximum file length: 500 lines
- ✅ Use named exports over default exports

## 3. Type Safety

### 3.1 Type Standards
- ✅ No 'any' types allowed
- ✅ Use strict TypeScript configuration
- ✅ Define interfaces over types when possible
- ✅ Export shared types from dedicated files
- ✅ Use enums for fixed sets of values

### 3.2 Type Conventions
```typescript
// Use PascalCase for types/interfaces
interface EntityName {}
// Use UPPER_SNAKE_CASE for enums
enum HTTP_METHOD {}
// Use PascalCase for type aliases
type RequestBody = {}
```

## 4. Error Handling

### 4.1 Standards
- ✅ Use custom error classes
- ✅ Always handle async errors
- ✅ Log errors before throwing
- ✅ Provide meaningful error messages
- ✅ Use error boundaries in React

### 4.2 Error Format
```typescript
try {
  // Operation
} catch (error) {
  handleError(error);
}
```

## 5. State Management

### 5.1 Standards
- ✅ Use Zustand for global state
- ✅ Keep component state local when possible
- ✅ Avoid prop drilling (max 3 levels)
- ✅ Use context for theme/auth
- ✅ Implement proper loading states

## 6. Testing

### 6.1 Test Requirements
- ✅ Unit tests for utilities
- ✅ Integration tests for queries
- ✅ Component tests with React Testing Library
- ✅ E2E tests for critical paths
- ✅ Maintain 80% code coverage

### 6.2 Test Standards
- ✅ Use descriptive test names
- ✅ Follow AAA pattern (Arrange, Act, Assert)
- ✅ Mock external dependencies
- ✅ Test error cases
- ✅ Keep tests maintainable

## 7. Performance

### 7.1 Standards
- ✅ Implement proper loading states
- ✅ Use pagination for lists
- ✅ Optimize images and assets
- ✅ Implement caching where appropriate
- ✅ Monitor bundle size

### 7.2 Metrics
- ✅ First contentful paint < 1.5s
- ✅ Time to interactive < 3.5s
- ✅ Bundle size < 250kb (initial)
- ✅ API response time < 500ms

## 8. Security

### 8.1 Standards
- ✅ Implement proper authentication
- ✅ Use HTTPS everywhere
- ✅ Sanitize user inputs
- ✅ Follow OWASP guidelines
- ✅ Regular security audits

## 9. Documentation

### 9.1 Requirements
- ✅ Document all APIs
- ✅ Keep README up to date
- ✅ Document complex logic
- ✅ Include setup instructions
- ✅ Document environment variables

### 9.2 Format
- ✅ Use JSDoc for functions
- ✅ Keep documentation close to code
- ✅ Include examples
- ✅ Document breaking changes

### 9.3 Documentation Standards

#### 9.3.1 Code Documentation
- ✅ All files must have a file-level JSDoc comment explaining its purpose
- ✅ All classes must have class-level JSDoc documentation
- ✅ All public methods must have JSDoc documentation
- ✅ All interfaces and types must have documentation
- ✅ Use @param, @returns, @throws tags where applicable
- ✅ Document complex algorithms and business logic
- ✅ Keep comments up-to-date with code changes

#### 9.3.2 Documentation Format
```typescript
/**
 * Brief description of the component/function
 * 
 * Detailed description if needed
 * 
 * @param {Type} paramName - Parameter description
 * @returns {Type} Description of return value
 * @throws {ErrorType} Description of when this error occurs
 * 
 * @example
 * ```typescript
 * // Usage example
 * ```
 */
```

#### 9.3.3 Automatic Documentation
- ✅ Use TypeDoc for automatic API documentation generation
- ✅ Configure documentation build in CI/CD pipeline
- ✅ Generate documentation on version releases
- ✅ Host documentation on project wiki or docs site

#### 9.3.4 Documentation Maintenance
- ✅ Update documentation when making code changes
- ✅ Review documentation in code reviews
- ✅ Keep README up to date with latest changes
- ✅ Document breaking changes in CHANGELOG

#### 9.3.5 Component Documentation
- ✅ Document component props using JSDoc
- ✅ Include usage examples for components
- ✅ Document component state and side effects
- ✅ Document component lifecycle methods

#### 9.3.6 API Documentation
- ✅ Document all API endpoints
- ✅ Include request/response examples
- ✅ Document error responses
- ✅ Keep API documentation in sync with implementation

#### 9.3.7 Database Documentation
- ✅ Document database schema
- ✅ Document table relationships
- ✅ Document indexes and constraints
- ✅ Keep ERD diagrams up to date

#### 9.3.8 Project Documentation
- ✅ Maintain up-to-date README
- ✅ Document setup instructions
- ✅ Document deployment process
- ✅ Document environment variables
- ✅ Document third-party integrations

## 10. Deployment

### 10.1 Standards
- ✅ Use semantic versioning
- ✅ Maintain staging environment
- ✅ Automated deployments
- ✅ Feature flags for big changes
- ✅ Proper environment configuration
