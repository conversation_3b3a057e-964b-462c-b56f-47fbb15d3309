/**
 * Worker Management Types Module
 * Contains type definitions for worker management and skills
 * @module types/worker
 */

/**
 * Worker interface
 * Defines structure of a workshop worker
 * 
 * @interface Worker
 * @property {string} [worker_id] - Unique worker identifier
 * @property {string} worker_name - Worker's full name
 * @property {boolean} is_active - Whether worker is currently active
 * @property {string} [shift_start] - Worker's shift start time
 * @property {string} [shift_end] - Worker's shift end time
 * @property {Date} [created_at] - Creation timestamp
 * @property {Date} [updated_at] - Last update timestamp
 */
export interface Worker {
    worker_id?: string;
    worker_name: string;
    is_active: boolean;
    shift_start?: string;
    shift_end?: string;
    created_at?: Date;
    updated_at?: Date;
}

/**
 * Worker process skill mapping
 * Maps workers to processes with skill levels
 * 
 * @interface WorkerProcessSkill
 * @property {string} worker_id - Reference to worker
 * @property {string} process_id - Reference to process
 * @property {number} skill_level - Worker's skill level (1-5)
 * @property {number} efficiency_factor - Worker's efficiency (0.1-2.0)
 * @property {Date} [created_at] - Creation timestamp
 * @property {Date} [updated_at] - Last update timestamp
 */
export interface WorkerProcessSkill {
    worker_id: string;
    process_id: string;
    skill_level: number;
    efficiency_factor: number;
    created_at?: Date;
    updated_at?: Date;
}

/**
 * Worker form fields interface
 * Form data structure for worker creation/update
 * 
 * @interface WorkerFormFields
 * @property {string} worker_name - Worker's full name
 * @property {boolean} is_active - Active status
 * @property {string} [shift_start] - Shift start time
 * @property {string} [shift_end] - Shift end time
 */
export interface WorkerFormFields {
    worker_name: string;
    is_active: boolean;
    shift_start?: string;
    shift_end?: string;
}

/**
 * Worker form field definitions
 * Configuration for worker form fields
 * 
 * @const {Array<Object>}
 */
export const workerFields = [
    {
        name: 'worker_name',
        label: 'Worker Name',
        type: 'text',
        required: true,
        maxLength: 100,
        placeholder: 'Enter worker name',
    },
    {
        name: 'shift_start',
        label: 'Shift Start',
        type: 'time',
        required: false,
    },
    {
        name: 'shift_end',
        label: 'Shift End',
        type: 'time',
        required: false,
    },
    {
        name: 'is_active',
        label: 'Active',
        type: 'checkbox',
        required: false,
    },
] as const;

/**
 * Worker skill form fields interface
 * Form data structure for worker skill mapping
 * 
 * @interface WorkerSkillFormFields
 * @property {string} process_id - Reference to process
 * @property {number} skill_level - Skill level (1-5)
 * @property {number} efficiency_factor - Efficiency factor (0.1-2.0)
 */
export interface WorkerSkillFormFields {
    process_id: string;
    skill_level: number;
    efficiency_factor: number;
}

/**
 * Worker skill form field definitions
 * Configuration for worker skill form fields
 * 
 * @const {Array<Object>}
 */
export const workerSkillFields = [
    {
        name: 'process_id',
        label: 'Process',
        type: 'select',
        required: true,
    },
    {
        name: 'skill_level',
        label: 'Skill Level',
        type: 'number',
        required: true,
        min: 1,
        max: 5,
        placeholder: 'Enter skill level (1-5)',
    },
    {
        name: 'efficiency_factor',
        label: 'Efficiency Factor',
        type: 'number',
        required: true,
        min: 0.1,
        max: 2.0,
        step: 0.1,
        placeholder: 'Enter efficiency factor',
    },
] as const;
