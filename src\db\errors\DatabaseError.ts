export class DatabaseError extends Error {
  constructor(
    message: string,
    public code: string,
    public originalError?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public fields: Record<string, string[]>
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends DatabaseError {
  constructor(entity: string, id: string | number) {
    super(`${entity} with id ${id} not found`, 'ENTITY_NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class UniqueConstraintError extends DatabaseError {
  constructor(field: string) {
    super(`${field} already exists`, 'UNIQUE_CONSTRAINT_VIOLATION');
    this.name = 'UniqueConstraintError';
  }
}

export class ForeignKeyError extends DatabaseError {
  constructor(message: string) {
    super(message, 'FOREIGN_KEY_CONSTRAINT_VIOLATION');
    this.name = 'ForeignKeyError';
  }
}
