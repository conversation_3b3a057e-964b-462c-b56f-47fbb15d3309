'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { FormField } from '../common/Form/FormField';

interface MasterDataFormProps {
  type: 'item_type' | 'karat' | 'gold_color' | 'order_category' | 'style';
  initialData?: any;
  onSubmit: (data: any) => Promise<void>;
  isSubmitting: boolean;
}

const formFields = {
  item_type: [
    { name: 'description', label: 'Description', type: 'text', required: true },
    { name: 'code', label: 'Code', type: 'text', required: true },
    { name: 'is_active', label: 'Active', type: 'checkbox' },
  ],
  karat: [
    { name: 'description', label: 'Description', type: 'text', required: true },
    { name: 'purity', label: 'Purity', type: 'number', required: true },
    { name: 'is_active', label: 'Active', type: 'checkbox' },
  ],
  gold_color: [
    { name: 'description', label: 'Description', type: 'text', required: true },
    { name: 'code', label: 'Code', type: 'text', required: true },
    { name: 'is_active', label: 'Active', type: 'checkbox' },
  ],
  order_category: [
    { name: 'description', label: 'Description', type: 'text', required: true },
    { name: 'code', label: 'Code', type: 'text', required: true },
    { name: 'priority', label: 'Priority', type: 'number', required: true },
    { name: 'is_active', label: 'Active', type: 'checkbox' },
  ],
  style: [
    { name: 'description', label: 'Description', type: 'text', required: true },
    { name: 'code', label: 'Code', type: 'text', required: true },
    { name: 'category_id', label: 'Category', type: 'select', required: true },
    { name: 'is_active', label: 'Active', type: 'checkbox' },
  ],
};

export function MasterDataForm({
  type,
  initialData,
  onSubmit,
  isSubmitting,
}: MasterDataFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: initialData || {},
  });

  const fields = formFields[type];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {fields.map((field) => (
        <FormField
          key={field.name}
          label={field.label}
          error={errors[field.name]?.message?.toString()}
        >
          {field.type === 'checkbox' ? (
            <input
              type="checkbox"
              {...register(field.name)}
              className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
          ) : field.type === 'select' ? (
            <select
              {...register(field.name, { required: field.required })}
              className="mt-1 block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
            >
              <option value="">Select {field.label}</option>
              {/* Options will be passed through props */}
            </select>
          ) : (
            <input
              type={field.type}
              {...register(field.name, { required: field.required })}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          )}
        </FormField>
      ))}

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex justify-center rounded-md border border-transparent bg-indigo-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
        >
          {isSubmitting ? 'Saving...' : 'Save'}
        </button>
      </div>
    </form>
  );
}
