'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Database, 
  Palette, 
  CircleDollarSign,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const metalMasters = [
  {
    label: 'Metal Types',
    href: '/masters/inventory?view=metal-types',
    icon: <Database className="w-8 h-8" />,
    description: 'Define and manage different types of metals used in jewelry manufacturing',
    color: 'text-blue-600'
  },
  {
    label: 'Metal Colors',
    href: '/masters/metal-colors',
    icon: <Palette className="w-8 h-8" />,
    description: 'Configure available metal colors and their properties',
    color: 'text-purple-600'
  },
  {
    label: 'Purities',
    href: '/masters/purities',
    icon: <CircleDollarSign className="w-8 h-8" />,
    description: 'Define metal purity levels and their specifications',
    color: 'text-yellow-600'
  }
];

export default function MetalsMasterPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/masters">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Masters
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Metals Management</h1>
          <p className="text-muted-foreground">Configure metal types, colors, and purity levels</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metalMasters.map((master) => (
          <Link key={master.href} href={master.href}>
            <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={master.color}>
                    {master.icon}
                  </div>
                  <CardTitle className="text-lg">{master.label}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {master.description}
                </CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
