'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  User, 
  Users, 
  Shapes, 
  BookOpen, 
  Palette, 
  CircleDollarSign, 
  Settings, 
  UserCog, 
  GraduationCap, 
  Factory, 
  Calendar, 
  Gem,
  Database,
  Circle,
  Box,
  Scissors,
  Scale
} from 'lucide-react';

const masterCategories = [
  {
    title: 'Customer Management',
    description: 'Manage customers and third-party relationships',
    items: [
      {
        label: 'Customer Management',
        href: '/masters/customers',
        icon: <User className="w-5 h-5" />,
        description: 'Manage customer information and profiles'
      },
      {
        label: 'Third Party Customers',
        href: '/masters/third-party-customers',
        icon: <Users className="w-5 h-5" />,
        description: 'Manage external customer relationships'
      }
    ]
  },
  {
    title: 'Product & Order Management',
    description: 'Configure product types and order categories',
    items: [
      {
        label: 'Item Types',
        href: '/masters/item-types',
        icon: <Shapes className="w-5 h-5" />,
        description: 'Define different types of jewelry items'
      },
      {
        label: 'Order Categories',
        href: '/masters/order-categories',
        icon: <BookOpen className="w-5 h-5" />,
        description: 'Categorize different types of orders'
      },
      {
        label: 'Styles',
        href: '/masters/styles',
        icon: <Gem className="w-5 h-5" />,
        description: 'Manage jewelry styles and designs'
      }
    ]
  },
  {
    title: 'Metals Management',
    description: 'Configure metal types, colors, and purity levels',
    items: [
      {
        label: 'Metal Types',
        href: '/masters/inventory?view=metal-types',
        icon: <Database className="w-5 h-5" />,
        description: 'Define available metal types'
      },
      {
        label: 'Metal Colors',
        href: '/masters/metal-colors',
        icon: <Palette className="w-5 h-5" />,
        description: 'Define available metal colors'
      },
      {
        label: 'Purities',
        href: '/masters/purities',
        icon: <CircleDollarSign className="w-5 h-5" />,
        description: 'Define metal purity levels'
      }
    ]
  },
  {
    title: 'Stones Management',
    description: 'Configure stone types, shapes, and properties',
    items: [
      {
        label: 'Stone Types',
        href: '/masters/inventory?view=stone-types',
        icon: <Circle className="w-5 h-5" />,
        description: 'Define available stone types'
      },
      {
        label: 'Stone Shapes',
        href: '/masters/inventory?view=stone-shapes',
        icon: <Box className="w-5 h-5" />,
        description: 'Define available stone shapes'
      }
    ]
  },
  {
    title: 'Process & Workflow',
    description: 'Configure manufacturing processes and workflows',
    items: [
      {
        label: 'Process Master',
        href: '/masters/processes',
        icon: <Settings className="w-5 h-5" />,
        description: 'Define manufacturing processes'
      },
      {
        label: 'Workshop Config',
        href: '/masters/workshop/config',
        icon: <Factory className="w-5 h-5" />,
        description: 'Configure workshop settings'
      }
    ]
  },
  {
    title: 'Human Resources',
    description: 'Manage workers, skills, and schedules',
    items: [
      {
        label: 'Worker Management',
        href: '/masters/workers',
        icon: <UserCog className="w-5 h-5" />,
        description: 'Manage worker information and assignments'
      },
      {
        label: 'Worker Skills',
        href: '/masters/workers/skills',
        icon: <GraduationCap className="w-5 h-5" />,
        description: 'Define and assign worker skills'
      },
      {
        label: 'Holidays',
        href: '/masters/holidays',
        icon: <Calendar className="w-5 h-5" />,
        description: 'Manage holiday calendar'
      }
    ]
  }
];

export default function MastersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Masters Management
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Configure and manage all master data for your jewelry manufacturing system
        </p>
      </div>

      <div className="grid gap-8">
        {masterCategories.map((category, categoryIndex) => (
          <div key={categoryIndex}>
            <div className="mb-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-1">
                {category.title}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {category.description}
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.items.map((item, itemIndex) => (
                <Link key={itemIndex} href={item.href}>
                  <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                    <CardHeader className="pb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                          {item.icon}
                        </div>
                        <CardTitle className="text-lg">{item.label}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="text-sm">
                        {item.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
