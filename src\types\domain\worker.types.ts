import { BaseEntity } from './common.types';

export interface Worker extends BaseEntity {
  worker_id: string;
  name: string;
  // code: string; // Removed - Not in worker_mast schema
  is_active: boolean;
  is_vendor: boolean;
  shift_start?: string | null; // Added from worker_mast schema
  shift_end?: string | null; // Added from worker_mast schema
  worker_type?: string | null; // Added from worker_mast schema
  available_from?: string | null; // Added from worker_mast schema
  available_to?: string | null; // Added from worker_mast schema
  efficiency_factor: number;
  // skills?: WorkerSkill[]; // Removed - Skills managed in worker_skills table
}

export interface WorkerSkill extends BaseEntity {
  skill_id: string; // Renamed from id to match worker_skills schema
  worker_id: string;
  process_id: string;
  skill_level: number;
  efficiency_factor?: number | null; // Added from worker_skills schema
  is_primary: boolean;
}

export interface WorkerAllocation extends BaseEntity {
  id: string;
  worker_id: string;
  process_id: string;
  start_time: string;
  end_time: string;
  allocated_hours: number;
}
