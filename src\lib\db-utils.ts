import { supabase } from './db';
import { Database } from '../types/supabase';

// Types for table names and their corresponding row types
type Tables = Database['public']['Tables'];
type TableNames = keyof Tables;
type TableRow<T extends TableNames> = Tables[T]['Row'];

// Generic type for query filters
type QueryFilter<T> = Partial<{
  [K in keyof T]: T[K] | { in: T[K][] } | { gt: T[K] } | { lt: T[K] } | { gte: T[K] } | { lte: T[K] };
}>;

// Generic CRUD operations
export const dbUtils = {
  // Create a new record
  async create<T extends TableNames>(
    table: T,
    data: Tables[T]['Insert']
  ): Promise<TableRow<T> | null> {
    const { data: result, error } = await supabase
      .from(table)
      .insert(data)
      .select()
      .single();

    if (error) throw error;
    return result;
  },

  // Read records with optional filters
  async read<T extends TableNames>(
    table: T,
    filters?: QueryFilter<TableRow<T>>,
    options?: {
      limit?: number;
      offset?: number;
      orderBy?: { column: keyof TableRow<T>; ascending?: boolean };
    }
  ): Promise<TableRow<T>[]> {
    let query = supabase.from(table).select('*');

    // Apply filters
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          const operator = Object.keys(value)[0];
          const filterValue = value[operator as keyof typeof value];
          switch (operator) {
            case 'in':
              query = query.in(key, filterValue as any[]);
              break;
            case 'gt':
              query = query.gt(key, filterValue);
              break;
            case 'lt':
              query = query.lt(key, filterValue);
              break;
            case 'gte':
              query = query.gte(key, filterValue);
              break;
            case 'lte':
              query = query.lte(key, filterValue);
              break;
          }
        } else {
          query = query.eq(key, value);
        }
      });
    }

    // Apply options
    if (options) {
      if (options.limit) query = query.limit(options.limit);
      if (options.offset) query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      if (options.orderBy) {
        query = query.order(options.orderBy.column as string, {
          ascending: options.orderBy.ascending ?? true,
        });
      }
    }

    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  // Update a record
  async update<T extends TableNames>(
    table: T,
    id: string,
    data: Tables[T]['Update'],
    idField: keyof TableRow<T> = 'id' as keyof TableRow<T>
  ): Promise<TableRow<T> | null> {
    const { data: result, error } = await supabase
      .from(table)
      .update(data)
      .eq(idField as string, id)
      .select()
      .single();

    if (error) throw error;
    return result;
  },

  // Delete a record
  async delete<T extends TableNames>(
    table: T,
    id: string,
    idField: keyof TableRow<T> = 'id' as keyof TableRow<T>
  ): Promise<void> {
    const { error } = await supabase
      .from(table)
      .delete()
      .eq(idField as string, id);

    if (error) throw error;
  },

  // Utility function to log activity
  async logActivity(
    userId: string | null,
    activityType: string,
    entityType: string,
    entityId: string,
    oldValue?: any,
    newValue?: any
  ) {
    const { error } = await supabase.from('activity_logs').insert({
      user_id: userId,
      activity_type: activityType,
      entity_type: entityType,
      entity_id: entityId,
      previous_state: oldValue,
      new_state: newValue,
      activity_time: new Date().toISOString(),
      ip_address: typeof window !== 'undefined' ? window.location.hostname : 'server',
    });

    if (error) throw error;
  },
};

// Export type utilities for use in other files
export type { TableNames, TableRow, QueryFilter };
