'use client';

import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import {
  ShoppingBag,
  Box,
  Users,
  Brush,
  Activity,
  ClipboardList,
  Settings,
  UserCog,
} from 'lucide-react';

interface QuickLink {
  title: string;
  description: string;
  href: string;
  icon: React.ElementType;
  roles: string[];
}

const quickLinks: QuickLink[] = [
  {
    title: 'Orders',
    description: 'View and manage production orders',
    href: '/orders',
    icon: ShoppingBag,
    roles: ['admin', 'data_entry', 'customer'],
  },
  {
    title: 'Active Processes',
    description: 'Track ongoing production processes',
    href: '/processes/active',
    icon: Activity,
    roles: ['admin', 'data_entry'],
  },
  {
    title: 'Workers',
    description: 'Manage worker assignments and efficiency',
    href: '/workers',
    icon: Users,
    roles: ['admin'],
  },
  {
    title: 'Process History',
    description: 'View completed processes and analytics',
    href: '/processes/history',
    icon: ClipboardList,
    roles: ['admin', 'data_entry'],
  },
  {
    title: 'Master Data',
    description: 'Configure system settings and master data',
    href: '/masters/item-types',
    icon: Settings,
    roles: ['admin'],
  },
  {
    title: 'Item Types',
    description: 'Browse available product types',
    href: '/masters/item-types',
    icon: Box,
    roles: ['admin', 'data_entry', 'customer'],
  },
  {
    title: 'Styles',
    description: 'View product styles and categories',
    href: '/masters/styles',
    icon: Brush,
    roles: ['admin', 'data_entry', 'customer'],
  },
];

export default function Home() {
  const { userRole, user } = useAuth();

  const filteredLinks = quickLinks.filter(
    (link) => userRole && link.roles.includes(userRole)
  );

  return (
    <main className="min-h-screen p-8">
      <div className="welcome-banner">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Welcome to JWL Process Management
        </h1>
        <p className="text-lg text-muted-foreground">
          Streamline your jewelry production workflow
        </p>
      </div>

      <section className="mb-12">
        <h2 className="section-title">Quick Links</h2>
        <div className="quick-links">
          {filteredLinks.map((link) => (
            <Link key={link.title} href={link.href} className="quick-link-card">
              <div className="p-4 rounded-full bg-primary/10 mr-4">
                <link.icon className="h-6 w-6 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">{link.title}</h3>
                <p className="text-sm text-muted-foreground">{link.description}</p>
              </div>
            </Link>
          ))}
        </div>
      </section>

      <section>
        <h2 className="section-title">Recent Activity</h2>
        <div className="grid gap-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-foreground mb-4">Process Updates</h3>
            {/* Add process updates component here */}
          </div>
        </div>
      </section>
    </main>
  );
}
