'use client';

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { format } from 'date-fns';
// Use consistent Worker type from domain
import { Worker } from '@/types/domain/worker.types'; 
// Removed unused WorkerBase import
// Use consistent Process types from @/types/process
import { ProcessSchedule, ProcessStatus } from '@/types/process'; 

interface WorkerListProps {
  worker: Worker; // Uses Worker from domain/worker.types
  schedules: ProcessSchedule[]; // Uses ProcessSchedule from @/types/process
  getStatusBadge: (status: ProcessStatus) => React.ReactNode; // Uses ProcessStatus from @/types/process
}

export function WorkerList({ worker, schedules, getStatusBadge }: WorkerListProps) {
  const { setNodeRef, isOver } = useDroppable({
    // Use worker.id as Worker type from domain uses 'id'
    id: worker.id, 
  });

  // TEMPORARY SOLUTION: Use type assertion to handle the Worker type mismatch
  // This should be fixed by consolidating Worker types per PROJECT_RULES.md Section 1.1
  type WorkerWithSkills = Worker & {
    primary_skills?: string[];
    efficiency_rating?: number;
  };

  // Cast to the extended type
  const workerWithSkills = worker as WorkerWithSkills;
  
  return (
    <div
      ref={setNodeRef}
      className={`p-4 rounded-lg border ${
        isOver ? 'border-primary bg-primary/10' : 'border-border'
      }`}
    >
      <div className="flex items-center justify-between mb-4">
        <div>
          <h4 className="font-medium">{worker.name}</h4>
          <p className="text-sm text-muted-foreground">
            {/* Using the cast worker object to access properties not in the base type */}
            {workerWithSkills.primary_skills?.join(', ') || 'No skills listed'}
          </p>
        </div>
        <div className="text-sm text-muted-foreground">
          Efficiency: {workerWithSkills.efficiency_rating ? `${workerWithSkills.efficiency_rating}%` : 'N/A'}
        </div>
      </div>

      <div className="space-y-2">
        {schedules.map((schedule) => (
          <div
            // Assuming ProcessSchedule has 'id' as primary key based on type definition
            key={schedule.id} 
            className="p-3 rounded-md bg-secondary/50"
          >
            <div className="flex items-center justify-between mb-1">
              <span className="font-medium">
                {/* Displaying schedule ID for context */}
                Process Schedule #{schedule.id}
              </span>
              {getStatusBadge(schedule.status)}
            </div>
            <div className="text-sm text-muted-foreground">
              {format(new Date(schedule.scheduled_start), 'HH:mm')} -{' '}
              {format(new Date(schedule.scheduled_end), 'HH:mm')}
              {/* Removed schedule.is_overtime as it's not in the ProcessSchedule type */}
            </div>
          </div>
        ))}

        {schedules.length === 0 && (
          <div className="text-sm text-muted-foreground text-center py-4">
            No processes scheduled
          </div>
        )}
      </div>
    </div>
  );
}
