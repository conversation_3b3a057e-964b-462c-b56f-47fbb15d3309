/**
 * MasterDataList Component
 * A reusable component for displaying and managing master data tables
 * 
 * @module components/masters
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Plus, Pencil } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

/**
 * Props for the MasterDataList component
 */
interface MasterDataListProps {
  /** Type of master data to display */
  type: 'item_type' | 'karat' | 'gold_color' | 'order_category' | 'style';
  
  /** Column definitions for the table */
  columns: {
    /** Key to access the data in each row */
    key: string;
    /** Display label for the column header */
    label: string;
    /** Optional render function for custom cell content */
    render?: (value: any) => React.ReactNode;
  }[];
}

/**
 * MasterDataList Component
 * Displays a table of master data with support for admin actions
 * 
 * @example
 * ```tsx
 * <MasterDataList
 *   type="item_type"
 *   columns={[
 *     { key: 'description', label: 'Description' },
 *     { key: 'suffix', label: 'Code' },
 *     {
 *       key: 'average_processing_time',
 *       label: 'Processing Time',
 *       render: (value) => `${value} minutes`
 *     }
 *   ]}
 * />
 * ```
 */
export function MasterDataList({ type, columns }: MasterDataListProps) {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAdmin } = useAuth();

  // Fetch master data on component mount and type change
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`/api/masters?table=${type}`, {
          credentials: 'include'
        });
        if (!response.ok) {
          throw new Error('Failed to fetch data');
        }
        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching master data:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [type]);

  // Show loading state
  if (loading) {
    return <div>Loading...</div>;
  }

  // Show error state
  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="space-y-4">
      {/* Add New button - only visible to admin users */}
      {isAdmin && (
        <div className="flex justify-end">
          <Link
            href={`/masters/${type}/new`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New
          </Link>
        </div>
      )}

      {/* Data table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  {column.label}
                </th>
              ))}
              {/* Actions column - only visible to admin users */}
              {isAdmin && (
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                >
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item) => (
              <tr key={item.id}>
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300"
                  >
                    {column.render
                      ? column.render(item[column.key])
                      : item[column.key]}
                  </td>
                ))}
                {/* Edit action - only visible to admin users */}
                {isAdmin && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/masters/${type}/${item.id}/edit`}
                      className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                    >
                      <Pencil className="h-4 w-4" />
                    </Link>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
