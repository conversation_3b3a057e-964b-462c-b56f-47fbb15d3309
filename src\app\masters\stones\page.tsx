'use client';

import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Circle,
  Box,
  Scale,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const stoneMasters = [
  {
    label: 'Stone Types',
    href: '/masters/inventory?view=stone-types',
    icon: <Circle className="w-8 h-8" />,
    description: 'Define and manage different types of stones used in jewelry',
    color: 'text-green-600'
  },
  {
    label: 'Stone Shapes',
    href: '/masters/inventory?view=stone-shapes',
    icon: <Box className="w-8 h-8" />,
    description: 'Configure available stone shapes and cutting styles',
    color: 'text-indigo-600'
  },
  {
    label: 'Stone Sizes',
    href: '/masters/inventory?view=stone-sizes',
    icon: <Scale className="w-8 h-8" />,
    description: 'Define stone size categories and specifications',
    color: 'text-purple-600'
  }
];

export default function StonesMasterPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/masters">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Masters
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Stones Management</h1>
          <p className="text-muted-foreground">Configure stone types, shapes, and properties</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stoneMasters.map((master) => (
          <Link key={master.href} href={master.href}>
            <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={master.color}>
                    {master.icon}
                  </div>
                  <CardTitle className="text-lg">{master.label}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {master.description}
                </CardDescription>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
