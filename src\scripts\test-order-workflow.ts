/**
 * Test script for order-centric workflow
 * 
 * This script tests the complete order → issue → process → receipt workflow
 * to ensure proper functionality of the order-centric material management system.
 */

import { createClient } from '@/lib/supabase/server';

async function testOrderWorkflow() {
  const supabase = createClient();
  
  console.log('🧪 Testing Order-Centric Workflow...\n');

  try {
    // 1. Test Orders API endpoint
    console.log('1️⃣ Testing Orders API...');
    const ordersResponse = await fetch('http://localhost:3000/api/masters/orders');
    const ordersData = await ordersResponse.json();
    
    if (ordersData.success) {
      console.log(`✅ Orders API working - Found ${ordersData.count} orders`);
      if (ordersData.data.length > 0) {
        console.log(`   Sample order: ${ordersData.data[0].order_reference_no} (${ordersData.data[0].customer_name})`);
      }
    } else {
      console.log('❌ Orders API failed:', ordersData.error);
    }

    // 2. Test Master Data Loading
    console.log('\n2️⃣ Testing Master Data Loading...');
    
    const [customersRes, workersRes, processesRes] = await Promise.all([
      supabase.from('customer_mast').select('customer_id, customer_name').limit(5),
      supabase.from('worker_mast').select('worker_id, worker_name').limit(5),
      supabase.from('process_mast').select('process_id, name, default_wastage_pct').limit(5)
    ]);

    if (customersRes.error) {
      console.log('❌ Customers query failed:', customersRes.error.message);
    } else {
      console.log(`✅ Customers loaded - Found ${customersRes.data.length} customers`);
    }

    if (workersRes.error) {
      console.log('❌ Workers query failed:', workersRes.error.message);
    } else {
      console.log(`✅ Workers loaded - Found ${workersRes.data.length} workers`);
    }

    if (processesRes.error) {
      console.log('❌ Processes query failed:', processesRes.error.message);
    } else {
      console.log(`✅ Processes loaded - Found ${processesRes.data.length} processes`);
      if (processesRes.data.length > 0) {
        console.log(`   Sample process: ${processesRes.data[0].name} (${processesRes.data[0].default_wastage_pct}% loss)`);
      }
    }

    // 3. Test Material Transaction Service Functions
    console.log('\n3️⃣ Testing Service Functions...');
    
    // Test getIssuedMaterials function
    try {
      const { getIssuedMaterials } = await import('@/services/materialTransactionService');
      const issuedMaterials = await getIssuedMaterials({
        material_type: 'metal'
      });
      console.log(`✅ getIssuedMaterials working - Found ${issuedMaterials?.length || 0} issued materials`);
    } catch (error) {
      console.log('❌ getIssuedMaterials failed:', error);
    }

    // Test getIssuedStones function
    try {
      const { getIssuedStones } = await import('@/services/stoneInventoryService');
      const issuedStones = await getIssuedStones({});
      console.log(`✅ getIssuedStones working - Found ${issuedStones?.length || 0} issued stones`);
    } catch (error) {
      console.log('❌ getIssuedStones failed:', error);
    }

    // Test getIssuedFindings function
    try {
      const { getIssuedFindings } = await import('@/services/findingsInventoryService');
      const issuedFindings = await getIssuedFindings({});
      console.log(`✅ getIssuedFindings working - Found ${issuedFindings?.length || 0} issued findings`);
    } catch (error) {
      console.log('❌ getIssuedFindings failed:', error);
    }

    // 4. Test Database Schema Consistency
    console.log('\n4️⃣ Testing Database Schema...');
    
    const schemaTests = [
      { table: 'orders', key_field: 'order_id' },
      { table: 'worker_mast', key_field: 'worker_id' },
      { table: 'process_mast', key_field: 'process_id' },
      { table: 'material_transactions', key_field: 'transaction_id' },
      { table: 'metal_type_mast', key_field: 'metal_type_id' },
      { table: 'karat_mast', key_field: 'karat_id' }
    ];

    for (const test of schemaTests) {
      try {
        const { data, error } = await supabase
          .from(test.table)
          .select(test.key_field)
          .limit(1);
        
        if (error) {
          console.log(`❌ Table ${test.table} failed:`, error.message);
        } else {
          console.log(`✅ Table ${test.table} accessible`);
        }
      } catch (error) {
        console.log(`❌ Table ${test.table} error:`, error);
      }
    }

    console.log('\n🎉 Order-Centric Workflow Test Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Export for use in other scripts
export { testOrderWorkflow };

// Run if called directly
if (require.main === module) {
  testOrderWorkflow();
}
