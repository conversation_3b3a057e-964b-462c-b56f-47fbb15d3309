/**
 * Material Receipt Details Page
 * Shows details of a customer material receipt and allows management of its items
 * 
 * @module app/inventory/material-receipt/[id]
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { FileEdit, ArrowLeft, Plus } from 'lucide-react';
import { CustomerMaterialReceipt } from '@/types/inventory';
import { useToast } from '@/hooks/useToast';
import { formatDate } from '@/lib/utils';

// Define a type that includes the customer name added by the API
type CustomerMaterialReceiptWithCustomerName = CustomerMaterialReceipt & {
  customer_name: string;
};

/**
 * Material Receipt Details Page Component
 * Displays receipt details and associated material items
 */
export default function MaterialReceiptDetailsPage() {
  const [receipt, setReceipt] = useState<CustomerMaterialReceiptWithCustomerName | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'metals' | 'diamonds' | 'stones' | 'polki'>('metals');
  const params = useParams();
  const router = useRouter();
  const { showToast } = useToast();
  const receiptId = params?.id as string;

  useEffect(() => {
    if (receiptId) {
      fetchReceipt(receiptId);
    }
  }, [receiptId]);

  /**
   * Fetches the material receipt by ID
   * @param id - The receipt ID to fetch
   */
  const fetchReceipt = async (id: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/inventory/material-receipts?id=${id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch material receipt');
      }
      
      const data = await response.json();
      
      if (!data) {
        throw new Error('Material receipt not found');
      }
      
      setReceipt(data);
      setError(null);
    } catch (err) {
      setError('Error loading material receipt. It may have been deleted or does not exist.');
      console.error('Error fetching material receipt:', err);
      showToast({
        title: 'Error loading material receipt',
        description: '',
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Renders the details section of the receipt
   */
  const renderReceiptDetails = () => {
    if (!receipt) return null;

    return (
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-semibold mb-4">Receipt Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Receipt ID</p>
            <p className="font-medium">{receipt.receipt_id}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Receipt Date</p>
            <p className="font-medium">{formatDate(receipt.receipt_date)}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Customer</p>
            <p className="font-medium">{receipt.customer_name}</p>
          </div>
          
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Order</p>
            <p className="font-medium">{receipt.order_id || '-'}</p>
          </div>
          
          {receipt.notes && (
            <div className="col-span-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">Notes</p>
              <p className="font-medium">{receipt.notes}</p>
            </div>
          )}
        </div>
        
        <div className="mt-4 flex justify-end">
          <button
            onClick={() => router.push(`/inventory/material-receipt/${receipt.receipt_id}/edit`)}
            className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <FileEdit className="w-4 h-4 mr-1" />
            Edit Receipt
          </button>
        </div>
      </div>
    );
  };

  /**
   * Renders the tabs for different material types
   */
  const renderMaterialTabs = () => {
    return (
      <div className="border-b border-gray-200 dark:border-gray-700">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              onClick={() => setActiveTab('metals')}
              className={`inline-block p-4 rounded-t-lg ${
                activeTab === 'metals'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
            >
              Metals
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => setActiveTab('diamonds')}
              className={`inline-block p-4 rounded-t-lg ${
                activeTab === 'diamonds'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
            >
              Diamonds
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => setActiveTab('stones')}
              className={`inline-block p-4 rounded-t-lg ${
                activeTab === 'stones'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
            >
              Stones
            </button>
          </li>
          <li>
            <button
              onClick={() => setActiveTab('polki')}
              className={`inline-block p-4 rounded-t-lg ${
                activeTab === 'polki'
                  ? 'text-blue-600 border-b-2 border-blue-600 dark:text-blue-500 dark:border-blue-500'
                  : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
              }`}
            >
              Polki
            </button>
          </li>
        </ul>
      </div>
    );
  };

  /**
   * Renders content based on the active material tab
   */
  const renderTabContent = () => {
    // In the future, this will display actual material items
    // For now, we'll just show a placeholder with an "Add" button
    return (
      <div className="py-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium capitalize">
            {activeTab === 'metals' ? 'Metal Items' : 
             activeTab === 'diamonds' ? 'Diamond Items' : 
             activeTab === 'stones' ? 'Stone Items' : 'Polki Items'}
          </h3>
          
          {receipt && (
            <button
              className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
              onClick={() => {
                // This will be implemented later to add specific material items
                showToast({
                  title: `Add ${activeTab} functionality will be implemented soon`,
                  description: '',
                  type: 'default'
                });
              }}
            >
              <Plus className="w-4 h-4 mr-1" />
              Add {activeTab.slice(0, -1).charAt(0).toUpperCase() + activeTab.slice(1, -1)}
            </button>
          )}
        </div>
        
        <div className="bg-gray-100 dark:bg-gray-700 p-8 rounded-lg flex justify-center items-center text-gray-500 dark:text-gray-400">
          <p>No {activeTab} items found. Add some using the button above.</p>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error || !receipt) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error || 'Material receipt not found'}</p>
        </div>
        <button
          onClick={() => router.push('/inventory/material-receipt')}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Back to Material Receipts
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <button
          onClick={() => router.push('/inventory/material-receipt')}
          className="flex items-center text-blue-600 dark:text-blue-400 hover:underline"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Material Receipts
        </button>
      </div>
      
      <h1 className="text-2xl font-bold mb-6">Material Receipt Details</h1>
      
      {renderReceiptDetails()}
      
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Material Items</h2>
        {renderMaterialTabs()}
        {renderTabContent()}
      </div>
    </div>
  );
}
