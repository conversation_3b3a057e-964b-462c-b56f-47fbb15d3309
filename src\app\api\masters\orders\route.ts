/**
 * @module api/masters/orders
 * @description API endpoint for fetching orders with customer information
 * 
 * Features:
 * - Order-centric data loading
 * - Customer information included
 * - Filtering by status and customer
 * - Proper error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    
    // Optional filters
    const customerId = searchParams.get('customer_id');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '100');

    let query = supabase
      .from('orders')
      .select(`
        order_id,
        order_reference_no,
        customer_id,
        order_date,
        delivery_date,
        status,
        total_amount,
        notes,
        customer:customer_mast(
          customer_id,
          customer_name,
          customer_code
        )
      `)
      .order('order_date', { ascending: false })
      .limit(limit);

    // Apply filters if provided
    if (customerId) {
      query = query.eq('customer_id', customerId);
    }
    
    if (status) {
      query = query.eq('status', status);
    }

    const { data: orders, error } = await query;

    if (error) {
      console.error('Error fetching orders:', error);
      return NextResponse.json(
        { error: 'Failed to fetch orders', details: error.message },
        { status: 500 }
      );
    }

    // Transform data to include customer name at order level for easier access
    const transformedOrders = orders?.map((order: any) => ({
      ...order,
      customer_name: order.customer?.customer_name || 'Unknown Customer',
      customer_code: order.customer?.customer_code || ''
    })) || [];

    return NextResponse.json({
      success: true,
      data: transformedOrders,
      count: transformedOrders.length
    });

  } catch (error) {
    console.error('Unexpected error in orders API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const body = await request.json();

    const { data: order, error } = await supabase
      .from('orders')
      .insert([{
        order_reference_no: body.order_reference_no,
        customer_id: body.customer_id,
        order_date: body.order_date || new Date().toISOString(),
        delivery_date: body.delivery_date,
        status: body.status || 'pending',
        total_amount: body.total_amount || 0,
        notes: body.notes
      }])
      .select(`
        *,
        customer:customer_mast(customer_name, customer_code)
      `)
      .single();

    if (error) {
      console.error('Error creating order:', error);
      return NextResponse.json(
        { error: 'Failed to create order', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: order
    });

  } catch (error) {
    console.error('Unexpected error creating order:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
