/**
 * Customers API Route Handler
 * Provides operations for customer data with role-based access control
 * 
 * @module api/masters/customers
 */

import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/middleware/withAuth';

/**
 * GET handler for customer data
 * Retrieves customer data with optional search functionality
 * 
 * @access Public - Accessible by admin, data_entry, and customer roles
 * @route GET /api/masters/customers?search={searchTerm}
 * 
 * @example
 * // Fetch all customers
 * GET /api/masters/customers
 * 
 * // Search for customers by name
 * GET /api/masters/customers?search=smith
 */
type CustomerData = {
  customer_id: string;
  description: string; // This is the actual column name in the database
};

export const GET = withAuth(['admin', 'data_entry', 'customer'])(
  async (req: NextRequest): Promise<NextResponse<CustomerData[] | { error: string }>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const searchParams = new URL(req.url).searchParams;
      const search = searchParams.get('search');

      // Only select the fields that exist in the database schema
      let query = supabase.from('customer_mast').select('customer_id, description');

      if (search) {
        query = query.ilike('description', `%${search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error fetching customer data:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST handler for customer data
 * Creates a new customer entry
 * 
 * @access Restricted - Admin only
 * @route POST /api/masters/customers
 */
export const POST = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<CustomerData | { error: string }>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const body = await req.json();

      const { data, error } = await supabase
        .from('customer_mast')
        .insert(body)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error creating customer:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * PATCH handler for customer data
 * Updates an existing customer entry
 * 
 * @access Restricted - Admin only
 * @route PATCH /api/masters/customers/{customerId}
 */
export const PATCH = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<CustomerData | { error: string }>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const { customer_id, ...updates } = await req.json();

      if (!customer_id) {
        return NextResponse.json(
          { error: 'Customer ID is required' },
          { status: 400 }
        );
      }

      const { data, error } = await supabase
        .from('customer_mast')
        .update(updates)
        .eq('customer_id', customer_id)
        .select()
        .single();

      if (error) throw error;

      return NextResponse.json(data);
    } catch (error) {
      console.error('Error updating customer:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE handler for customer data
 * Deletes an existing customer entry
 * 
 * @access Restricted - Admin only
 * @route DELETE /api/masters/customers/{customerId}
 */
export const DELETE = withAuth(['admin'])(
  async (req: NextRequest): Promise<NextResponse<{ success: boolean } | { error: string }>> => {
    try {
      const cookieStore = cookies();
      const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
      const searchParams = new URL(req.url).searchParams;
      const customerId = searchParams.get('customer_id');

      if (!customerId) {
        return NextResponse.json(
          { error: 'Customer ID is required' },
          { status: 400 }
        );
      }

      const { error } = await supabase
        .from('customer_mast')
        .delete()
        .eq('customer_id', customerId);

      if (error) throw error;

      return NextResponse.json({ success: true });
    } catch (error) {
      console.error('Error deleting customer:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  }
);
