/**
 * LossAnalysisDashboard Component
 * Comprehensive dashboard for analyzing material losses across processes
 * 
 * @module components/reports/loss
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/hooks/useToast';
// import { LossCategory } from '@/types/process-batch'; // Original problematic import

// Placeholder LossCategory type - to be refined with actual loss categories
type LossCategory = 'PROCESS_WASTAGE' | 'HANDLING_LOSS' | 'DEFECT_REWORK' | 'UNACCOUNTED' | 'THEFT_DAMAGE';
import { Loader2, Download, Filter, BarChart as BarChartIcon, PieChart as PieChartIcon } from 'lucide-react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Types for the loss analysis data
interface ProcessLossData {
  process_id: string;
  process_name: string;
  total_expected: number;
  total_actual: number;
  total_loss: number;
  loss_percentage: number;
}

interface LossCategoryData {
  category: LossCategory;
  category_name: string;
  total_loss: number;
  loss_percentage: number;
}

interface WorkerLossData {
  worker_id: string;
  worker_name: string;
  total_loss: number;
  processes_count: number;
  average_loss_per_process: number;
}

interface DateRangeFilter {
  startDate: string;
  endDate: string;
}

interface FilterOptions {
  dateRange: DateRangeFilter;
  processId?: string;
  workerId?: string;
  lossCategory?: LossCategory;
}

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658'];

// Helper function to format loss category names
const formatLossCategory = (category: LossCategory): string => {
  return category.replace(/_/g, ' ').toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

/**
 * LossAnalysisDashboard Component
 * Comprehensive dashboard for analyzing material losses across processes
 */
export const LossAnalysisDashboard: React.FC = () => {
  const { showToast } = useToast();
  
  const [activeTab, setActiveTab] = useState<string>('by-process');
  const [loading, setLoading] = useState<boolean>(true);
  const [showFilters, setShowFilters] = useState<boolean>(false);
  
  // Data states
  const [processList, setProcessList] = useState<{value: string, label: string}[]>([]);
  const [workerList, setWorkerList] = useState<{value: string, label: string}[]>([]);
  const [processLossData, setProcessLossData] = useState<ProcessLossData[]>([]);
  const [categoryLossData, setCategoryLossData] = useState<LossCategoryData[]>([]);
  const [workerLossData, setWorkerLossData] = useState<WorkerLossData[]>([]);
  
  // Filter state
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: {
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    }
  });
  
  // Load filter options on component mount
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        // Fetch processes
        const processRes = await fetch('/api/masters/processes');
        if (processRes.ok) {
          const processData = await processRes.json();
          setProcessList(processData.map((p: any) => ({
            value: p.process_id,
            label: p.name
          })));
        }
        
        // Fetch workers
        const workerRes = await fetch('/api/workers');
        if (workerRes.ok) {
          const workerData = await workerRes.json();
          setWorkerList(workerData.map((w: any) => ({
            value: w.worker_id,
            label: w.name
          })));
        }
      } catch (error) {
        console.error('Error fetching filter options:', error);
        showToast({
          title: 'Error',
          description: 'Failed to load filter options',
          type: 'destructive'
        });
      }
    };
    
    fetchFilterOptions();
  }, []);
  
  // Fetch data based on active tab and filters
  useEffect(() => {
    fetchData();
  }, [activeTab, filters]);
  
  // Fetch data based on active tab
  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Prepare query parameters
      const params = new URLSearchParams();
      if (filters.dateRange.startDate) {
        params.append('fromDate', filters.dateRange.startDate);
      }
      if (filters.dateRange.endDate) {
        params.append('toDate', filters.dateRange.endDate);
      }
      if (filters.processId) {
        params.append('processId', filters.processId);
      }
      if (filters.workerId) {
        params.append('workerId', filters.workerId);
      }
      if (filters.lossCategory) {
        params.append('lossCategory', filters.lossCategory);
      }
      
      // Fetch data based on active tab
      if (activeTab === 'by-process' || activeTab === 'all') {
        const processRes = await fetch(`/api/reports/loss/by-process?${params.toString()}`);
        if (processRes.ok) {
          const data = await processRes.json();
          setProcessLossData(data);
        } else {
          throw new Error('Failed to fetch process loss data');
        }
      }
      
      if (activeTab === 'by-category' || activeTab === 'all') {
        const categoryRes = await fetch(`/api/reports/loss/by-category?${params.toString()}`);
        if (categoryRes.ok) {
          const data = await categoryRes.json();
          setCategoryLossData(data);
        } else {
          throw new Error('Failed to fetch category loss data');
        }
      }
      
      if (activeTab === 'by-worker' || activeTab === 'all') {
        const workerRes = await fetch(`/api/reports/loss/by-worker?${params.toString()}`);
        if (workerRes.ok) {
          const data = await workerRes.json();
          setWorkerLossData(data);
        } else {
          throw new Error('Failed to fetch worker loss data');
        }
      }
    } catch (error) {
      console.error('Error fetching loss data:', error);
      showToast({
        title: 'Error',
        description: 'Failed to load loss analysis data',
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Handle filter changes
  const handleFilterChange = (field: string, value: any) => {
    if (field.startsWith('dateRange.')) {
      const dateField = field.split('.')[1];
      setFilters(prev => ({
        ...prev,
        dateRange: {
          ...prev.dateRange,
          [dateField]: value
        }
      }));
    } else {
      setFilters(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };
  
  // Reset filters
  const resetFilters = () => {
    setFilters({
      dateRange: {
        startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
        endDate: new Date().toISOString().split('T')[0]
      }
    });
  };
  
  // Export data as CSV
  const exportData = () => {
    let data: any[] = [];
    let filename = 'loss-analysis';
    
    if (activeTab === 'by-process') {
      data = processLossData;
      filename = 'process-loss-analysis';
    } else if (activeTab === 'by-category') {
      data = categoryLossData;
      filename = 'category-loss-analysis';
    } else if (activeTab === 'by-worker') {
      data = workerLossData;
      filename = 'worker-loss-analysis';
    }
    
    if (data.length === 0) {
      showToast({
        title: 'Error',
        description: 'No data to export',
        type: 'destructive'
      });
      return;
    }
    
    // Convert data to CSV
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map(item => Object.values(item).join(','));
    const csv = [headers, ...rows].join('\n');
    
    // Create download link
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Material Loss Analysis</h1>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={() => setShowFilters(!showFilters)}>
            <Filter className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={exportData}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Date Range */}
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={filters.dateRange.startDate}
                  onChange={(e) => handleFilterChange('dateRange.startDate', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={filters.dateRange.endDate}
                  onChange={(e) => handleFilterChange('dateRange.endDate', e.target.value)}
                />
              </div>
              
              {/* Process Filter */}
              <div className="space-y-2">
                <Label htmlFor="processId">Process</Label>
                <Select
                  value={filters.processId || ''}
                  // onValueChange={(value) => handleFilterChange('processId', value || undefined)} // Temporarily commented out for debugging type error
                >
                  <SelectTrigger id="processId">
                    <SelectValue /* placeholder="All Processes" */ />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Processes</SelectItem>
                    {processList.map((process) => (
                      <SelectItem key={process.value} value={process.value}>
                        {process.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Worker Filter */}
              <div className="space-y-2">
                <Label htmlFor="workerId">Worker</Label>
                <Select
                  value={filters.workerId || ''}
                  // onValueChange={(value) => handleFilterChange('workerId', value || undefined)} // Temporarily commented out
                >
                  <SelectTrigger id="workerId">
                    <SelectValue /* placeholder="All Workers" */ />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Workers</SelectItem>
                    {workerList.map((worker) => (
                      <SelectItem key={worker.value} value={worker.value}>
                        {worker.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {/* Loss Category Filter */}
              <div className="space-y-2">
                <Label htmlFor="lossCategory">Loss Category</Label>
                <Select
                  value={filters.lossCategory || ''}
                  // onValueChange={(value) => handleFilterChange('lossCategory', value || undefined)} // Temporarily commented out
                >
                  <SelectTrigger id="lossCategory">
                    <SelectValue /* placeholder="All Categories" */ />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    <SelectItem value="MELTING_LOSS">Melting Loss</SelectItem>
                    <SelectItem value="CUTTING_LOSS">Cutting Loss</SelectItem>
                    <SelectItem value="FILING_LOSS">Filing Loss</SelectItem>
                    <SelectItem value="POLISHING_LOSS">Polishing Loss</SelectItem>
                    <SelectItem value="SETTING_LOSS">Setting Loss</SelectItem>
                    <SelectItem value="RHODIUM_LOSS">Rhodium Loss</SelectItem>
                    <SelectItem value="MEASUREMENT_ERROR">Measurement Error</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Reset Button */}
              <div className="flex items-end">
                <Button variant="outline" onClick={resetFilters}>
                  Reset Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Tabs */}
      <Tabs defaultValue="by-process" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="by-process">
            <BarChartIcon className="h-4 w-4 mr-2" />
            By Process
          </TabsTrigger>
          <TabsTrigger value="by-category">
            <PieChartIcon className="h-4 w-4 mr-2" />
            By Loss Category
          </TabsTrigger>
          <TabsTrigger value="by-worker">
            <BarChartIcon className="h-4 w-4 mr-2" />
            By Worker
          </TabsTrigger>
        </TabsList>
        
        {/* Process Loss Tab */}
        <TabsContent value="by-process">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Process Loss Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : processLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={processLossData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="process_name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total_expected" name="Expected" fill="#8884d8" />
                      <Bar dataKey="total_actual" name="Actual" fill="#82ca9d" />
                      <Bar dataKey="total_loss" name="Loss" fill="#ff8042" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            {/* Table */}
            <Card>
              <CardHeader>
                <CardTitle>Process Loss Details</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : processLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Process</TableHead>
                        <TableHead className="text-right">Expected</TableHead>
                        <TableHead className="text-right">Actual</TableHead>
                        <TableHead className="text-right">Loss</TableHead>
                        <TableHead className="text-right">Loss %</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {processLossData.map((item) => (
                        <TableRow key={item.process_id}>
                          <TableCell className="font-medium">{item.process_name}</TableCell>
                          <TableCell className="text-right">{item.total_expected.toFixed(3)}</TableCell>
                          <TableCell className="text-right">{item.total_actual.toFixed(3)}</TableCell>
                          <TableCell className="text-right">{item.total_loss.toFixed(3)}</TableCell>
                          <TableCell className="text-right">{item.loss_percentage.toFixed(2)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Category Loss Tab */}
        <TabsContent value="by-category">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Loss Category Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : categoryLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={400}>
                    <PieChart>
                      <Pie
                        data={categoryLossData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={150}
                        fill="#8884d8"
                        dataKey="total_loss"
                        nameKey="category_name"
                      >
                        {categoryLossData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => typeof value === 'number' ? value.toFixed(3) : value} />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            {/* Table */}
            <Card>
              <CardHeader>
                <CardTitle>Loss Category Details</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : categoryLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead className="text-right">Total Loss</TableHead>
                        <TableHead className="text-right">Loss %</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categoryLossData.map((item) => (
                        <TableRow key={item.category}>
                          <TableCell className="font-medium">{item.category_name}</TableCell>
                          <TableCell className="text-right">{item.total_loss.toFixed(3)}</TableCell>
                          <TableCell className="text-right">{item.loss_percentage.toFixed(2)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Worker Loss Tab */}
        <TabsContent value="by-worker">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Worker Loss Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : workerLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={workerLossData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="worker_name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="total_loss" name="Total Loss" fill="#8884d8" />
                      <Bar dataKey="average_loss_per_process" name="Avg Loss/Process" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
            
            {/* Table */}
            <Card>
              <CardHeader>
                <CardTitle>Worker Loss Details</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center h-80">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : workerLossData.length === 0 ? (
                  <div className="flex justify-center items-center h-80 text-muted-foreground">
                    No data available for the selected filters
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Worker</TableHead>
                        <TableHead className="text-right">Processes</TableHead>
                        <TableHead className="text-right">Total Loss</TableHead>
                        <TableHead className="text-right">Avg Loss/Process</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {workerLossData.map((item) => (
                        <TableRow key={item.worker_id}>
                          <TableCell className="font-medium">{item.worker_name}</TableCell>
                          <TableCell className="text-right">{item.processes_count}</TableCell>
                          <TableCell className="text-right">{item.total_loss.toFixed(3)}</TableCell>
                          <TableCell className="text-right">{item.average_loss_per_process.toFixed(3)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
