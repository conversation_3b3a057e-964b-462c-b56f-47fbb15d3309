/**
 * Authentication Store
 * Manages user authentication state and operations using Zustand
 * @module store/authStore
 */

import { create } from 'zustand';
import { AuthStore } from './types';
// Removed incorrect import
// import { User, UserRole } from '@/types/auth'; 
import { supabase } from '@/lib/db';
// Import User type from Supabase library
import type { User } from '@supabase/supabase-js'; 

// Define UserRole locally based on usage
type UserRole = string | null;

/**
 * Authentication store using Zustand
 * Manages user state, role, and authentication operations
 * 
 * @example
 * ```typescript
 * // Using the auth store in a component
 * const { user, signIn, signOut } = useAuthStore();
 * 
 * // Sign in
 * await signIn('<EMAIL>', 'password');
 * 
 * // Access user info
 * console.log(user?.email);
 * 
 * // Sign out
 * await signOut();
 * ```
 */
export const useAuthStore = create<AuthStore>((set) => ({
  user: null,
  userRole: null,
  loading: true,
  isAuthenticated: false,

  /**
   * Updates user state in the store
   * @param {User | null} user - User object or null
   */
  setUser: (user: User | null) => 
    set({ user, isAuthenticated: !!user, loading: false }),

  /**
   * Updates user role in the store
   * @param {UserRole | null} role - User's role
   */
  setUserRole: (role: UserRole | null) => 
    set({ userRole: role }),

  /**
   * Signs in a user with email and password
   * Also fetches and sets the user's role
   * 
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @throws {Error} If sign in fails
   * 
   * @example
   * ```typescript
   * try {
   *   await signIn('<EMAIL>', 'password');
   *   // User is now signed in
   * } catch (error) {
   *   console.error('Sign in failed:', error);
   * }
   * ```
   */
  signIn: async (email: string, password: string) => {
    try {
      const { data: { user }, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (signInError) throw signInError;
      if (!user) throw new Error('No user returned after sign in');

      // Fetch user role
      const { data: roleData, error: roleError } = await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', user.id)
        .single();

      if (roleError) throw roleError;

      set({
        user,
        userRole: roleData?.role || null,
        isAuthenticated: true,
        loading: false,
      });
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  },

  /**
   * Creates a new user account and profile
   * 
   * @param {string} email - User's email
   * @param {string} password - User's password
   * @param {UserRole} [role='customer'] - User's role
   * @throws {Error} If sign up fails
   * 
   * @example
   * ```typescript
   * try {
   *   await signUp('<EMAIL>', 'password', 'data_entry');
   *   // New user account created
   * } catch (error) {
   *   console.error('Sign up failed:', error);
   * }
   * ```
   */
  signUp: async (email: string, password: string, role: UserRole = 'customer') => {
    try {
      const { data: { user }, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
      });

      if (signUpError) throw signUpError;
      if (!user) throw new Error('No user returned after sign up');

      // Create user role
      const { error: roleError } = await supabase
        .from('user_roles')
        .insert([
          {
            user_id: user.id,
            role,
          },
        ]);

      if (roleError) throw roleError;

      set({
        user,
        userRole: role,
        isAuthenticated: true,
        loading: false,
      });
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  },

  /**
   * Signs out the current user
   * Clears all user-related state
   * 
   * @throws {Error} If sign out fails
   * 
   * @example
   * ```typescript
   * try {
   *   await signOut();
   *   // User is now signed out
   * } catch (error) {
   *   console.error('Sign out failed:', error);
   * }
   * ```
   */
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      set({
        user: null,
        userRole: null,
        isAuthenticated: false,
        loading: false,
      });
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  },
}));
