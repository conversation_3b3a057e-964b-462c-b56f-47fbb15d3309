import { supabase } from '@/lib/db';
import { ItemTypeMaster } from '@/types/masters';

export async function getLastOrderSequence(yearMonth: string): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('order_id')
      .like('order_id', `${yearMonth}%`)
      .order('order_id', { ascending: false })
      .limit(1);

    if (error) throw error;

    if (!data || data.length === 0) return 0;

    // Extract sequence number from order ID (positions 4-7)
    const sequence = parseInt(data[0].order_id.substring(4, 7));
    return isNaN(sequence) ? 0 : sequence;
  } catch (error) {
    console.error('Error fetching last order sequence:', error);
    return 0;
  }
}

export async function isOrderIdUnique(orderId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('order_id')
      .eq('order_id', orderId)
      .limit(1);

    if (error) throw error;
    return !data || data.length === 0;
  } catch (error) {
    console.error('Error checking order ID uniqueness:', error);
    return false;
  }
}

export function generateOrderId(sequence: number, itemType?: ItemTypeMaster): string {
  const date = new Date();
  const yearMonth = `${date.getFullYear().toString().slice(-2)}${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}`;
  
  const sequenceStr = (sequence + 1).toString().padStart(3, '0');
  const suffix = itemType?.suffix || 'XX';

  return `${yearMonth}${sequenceStr}${suffix}`;
}

export function validateOrderId(orderId: string): boolean {
  // Check format: YYMMXXXTT
  // YY: 2-digit year
  // MM: 2-digit month (01-12)
  // XXX: 3-digit sequence (001-999)
  // TT: 2-character item type
  const regex = /^\d{2}(0[1-9]|1[0-2])\d{3}[A-Z]{2}$/;
  
  if (!regex.test(orderId)) return false;
  
  // Check sequence range (001-999)
  const sequence = parseInt(orderId.substring(4, 7));
  return sequence >= 1 && sequence <= 999;
}
