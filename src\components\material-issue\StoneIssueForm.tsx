/**
 * @module components/material-issue/StoneIssueForm
 * @description Stone Issue Form Component - Multi-stone issue form with customer segregation
 */

'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// Removed incorrect Select imports - using native select elements instead
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { issueStones, getAvailableStones } from '@/services/stoneInventoryService';

const stoneIssueSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  order_id: z.string().min(1, 'Order is required'),
  worker_id: z.string().min(1, 'Worker is required'),
  process_id: z.string().min(1, 'Process is required'),
  stone_type_id: z.string().optional(),
  stone_shape_id: z.string().optional(),
  stone_size_id: z.string().optional(),
  // stone_quality_id removed - automatically uses "Standard" quality
  notes: z.string().optional()
});

interface StoneIssueFormData {
  customer_id: string;
  order_id: string;
  worker_id: string;
  process_id: string;
  stone_type_id?: string;
  stone_shape_id?: string;
  stone_size_id?: string;
  // stone_quality_id removed - automatically uses "Standard" quality
  notes?: string;
}

interface StoneInventoryItem {
  inventory_id: string;
  stone_type_id: string;
  stone_shape_id: string;
  stone_size_id: string;
  stone_quality_id: string;
  quantity: number;
  weight_carats?: number; // Optional since API might not always include this
  status: string | null;
  customer_id: string;
  location: string;
  order_id: string | null;
  notes: string | null;
  created_at: string | null;
  updated_at: string | null;
  stone_details?: any;
  stone_shape?: any;
  stone_size?: any;
  stone_quality?: any;
}

export function StoneIssueForm() {
  const [customers, setCustomers] = useState([]);
  const [orders, setOrders] = useState([]);
  const [workers, setWorkers] = useState([]);
  const [processes, setProcesses] = useState([]);
  const [stoneTypes, setStoneTypes] = useState([]);
  const [stoneShapes, setStoneShapes] = useState([]);
  const [stoneSizes, setStoneSizes] = useState([]);
  // const [stoneQualities, setStoneQualities] = useState([]); // Hidden - using Standard quality automatically
  const [availableStones, setAvailableStones] = useState<StoneInventoryItem[]>([]);
  const [selectedStones, setSelectedStones] = useState<Array<{
    inventory_id: string;
    quantity: number;
    weight_carats: number;
  }>>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<StoneIssueFormData>({
    resolver: zodResolver(stoneIssueSchema),
    defaultValues: {
      customer_id: '',
      order_id: '',
      worker_id: '',
      process_id: '',
      stone_type_id: '',
      stone_shape_id: '',
      stone_size_id: '',
      // stone_quality_id removed - automatically uses "Standard" quality
      notes: ''
    }
  });

  // Load master data on component mount
  useEffect(() => {
    loadMasterData();
  }, []);

  // Load available stones when customer changes
  useEffect(() => {
    if (selectedCustomerId) {
      loadAvailableStones();
    }
  }, [selectedCustomerId, form.watch('stone_type_id'), form.watch('stone_shape_id'),
      form.watch('stone_size_id')]); // Removed stone_quality_id watch

  const loadMasterData = async () => {
    try {
      setIsLoading(true);
      
      // Load all master data in parallel (excluding stone qualities - using Standard automatically)
      const [customersRes, workersRes, processesRes, stoneTypesRes, stoneShapesRes, stoneSizesRes] = await Promise.all([
        fetch('/api/masters/customers').then(res => res.json()),
        fetch('/api/masters/workers').then(res => res.json()),
        fetch('/api/masters/processes').then(res => res.json()),
        fetch('/api/masters/stone-types').then(res => res.json()),
        fetch('/api/masters/stone-shapes').then(res => res.json()),
        fetch('/api/masters/stone-sizes').then(res => res.json())
        // Removed stone-qualities fetch - using Standard quality automatically
      ]);

      setCustomers(customersRes.data || []);
      setWorkers(workersRes.data || []);
      setProcesses(processesRes.data || []);
      setStoneTypes(stoneTypesRes.data || []);
      setStoneShapes(stoneShapesRes.data || []);
      setStoneSizes(stoneSizesRes.data || []);
      // setStoneQualities removed - using Standard quality automatically

    } catch (error) {
      console.error('Error loading master data:', error);
      toast.error('Failed to load master data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCustomerOrders = async (customerId: string) => {
    try {
      const response = await fetch(`/api/orders?customer_id=${customerId}&status=active`);
      const data = await response.json();
      setOrders(data.data || []);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      toast.error('Failed to load customer orders');
    }
  };

  const loadAvailableStones = async () => {
    if (!selectedCustomerId) return;

    try {
      const stones = await getAvailableStones({
        customer_id: selectedCustomerId,
        stone_type_id: form.watch('stone_type_id'),
        stone_shape_id: form.watch('stone_shape_id'),
        stone_size_id: form.watch('stone_size_id')
        // stone_quality_id removed - service will use Standard quality automatically
      });

      setAvailableStones(stones);
    } catch (error) {
      console.error('Error loading available stones:', error);
      toast.error('Failed to load available stones');
    }
  };

  const handleCustomerChange = (customerId: string) => {
    setSelectedCustomerId(customerId);
    form.setValue('customer_id', customerId);
    form.setValue('order_id', '');
    setOrders([]);
    setSelectedStones([]);
    loadCustomerOrders(customerId);
  };

  const handleStoneSelection = (stoneId: string, isSelected: boolean) => {
    if (isSelected) {
      const stone = availableStones.find(s => s.inventory_id === stoneId);
      if (stone) {
        setSelectedStones(prev => [...prev, {
          inventory_id: stoneId,
          quantity: 1,
          weight_carats: stone.weight_carats ? stone.weight_carats / stone.quantity : 0 // per piece weight
        }]);
      }
    } else {
      setSelectedStones(prev => prev.filter(s => s.inventory_id !== stoneId));
    }
  };

  const updateStoneQuantity = (stoneId: string, quantity: number) => {
    setSelectedStones(prev => prev.map(stone =>
      stone.inventory_id === stoneId
        ? { ...stone, quantity: Math.max(1, quantity) }
        : stone
    ));
  };

  const onSubmit = async (data: StoneIssueFormData) => {
    if (selectedStones.length === 0) {
      toast.error('Please select at least one stone to issue');
      return;
    }

    try {
      setIsSubmitting(true);

      // Issue each selected stone
      for (const selectedStone of selectedStones) {
        const stone = availableStones.find(s => s.inventory_id === selectedStone.inventory_id);
        if (!stone) continue;

        await issueStones({
          customer_id: data.customer_id,
          order_id: data.order_id,
          stone_type_id: stone.stone_type_id,
          stone_shape_id: stone.stone_shape_id,
          stone_size_id: stone.stone_size_id,
          // stone_quality_id removed - service uses Standard quality automatically
          quantity: selectedStone.quantity,
          total_weight_carats: selectedStone.weight_carats * selectedStone.quantity,
          worker_id: data.worker_id,
          process_id: data.process_id,
          issued_by: 'current_user', // TODO: Get from auth context
          notes: data.notes
        });
      }

      toast.success('Stones issued successfully');
      
      // Reset form and selections
      form.reset();
      setSelectedStones([]);
      setSelectedCustomerId('');
      setAvailableStones([]);
      
    } catch (error) {
      console.error('Error issuing stones:', error);
      toast.error('Failed to issue stones');
    } finally {
      setIsSubmitting(false);
    }
  };

  const totalSelectedQuantity = selectedStones.reduce((sum, stone) => sum + stone.quantity, 0);
  const totalSelectedWeight = selectedStones.reduce((sum, stone) => sum + (stone.weight_carats * stone.quantity), 0);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Issue Stones to Worker</CardTitle>
          <CardDescription>
            Select customer, order, and stones to issue for processing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Selection */}
              <div className="space-y-2">
                <Label htmlFor="customer_id">Customer *</Label>
                <select
                  id="customer_id"
                  value={selectedCustomerId}
                  onChange={(e) => handleCustomerChange(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select customer</option>
                  {customers.map((customer: any) => (
                    <option key={customer.customer_id} value={customer.customer_id}>
                      {customer.customer_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Order Selection */}
              <div className="space-y-2">
                <Label htmlFor="order_id">Order *</Label>
                <select
                  id="order_id"
                  disabled={!selectedCustomerId}
                  {...form.register('order_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50"
                >
                  <option value="">Select order</option>
                  {orders.map((order: any) => (
                    <option key={order.order_id} value={order.order_id}>
                      {order.order_no} - {order.style_code}
                    </option>
                  ))}
                </select>
              </div>

              {/* Worker Selection */}
              <div className="space-y-2">
                <Label htmlFor="worker_id">Worker *</Label>
                <select
                  id="worker_id"
                  {...form.register('worker_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select worker</option>
                  {workers.map((worker: any) => (
                    <option key={worker.worker_id} value={worker.worker_id}>
                      {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Process Selection */}
              <div className="space-y-2">
                <Label htmlFor="process_id">Process *</Label>
                <select
                  id="process_id"
                  {...form.register('process_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">Select process</option>
                  {processes.map((process: any) => (
                    <option key={process.process_id} value={process.process_id}>
                      {process.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Stone Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="stone_type_id">Stone Type</Label>
                <select
                  id="stone_type_id"
                  {...form.register('stone_type_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">All types</option>
                  {stoneTypes.map((type: any) => (
                    <option key={type.stone_type_id} value={type.stone_type_id}>
                      {type.type_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stone_shape_id">Shape</Label>
                <select
                  id="stone_shape_id"
                  {...form.register('stone_shape_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">All shapes</option>
                  {stoneShapes.map((shape: any) => (
                    <option key={shape.stone_shape_id} value={shape.stone_shape_id}>
                      {shape.shape_name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="stone_size_id">Size</Label>
                <select
                  id="stone_size_id"
                  {...form.register('stone_size_id')}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                >
                  <option value="">All sizes</option>
                  {stoneSizes.map((size: any) => (
                    <option key={size.stone_size_id} value={size.stone_size_id}>
                      {size.size_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Quality selection removed - automatically uses "Standard" quality */}
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                {...form.register('notes')}
                placeholder="Enter any additional notes..."
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Available Stones */}
      {selectedCustomerId && (
        <Card>
          <CardHeader>
            <CardTitle>Available Stones</CardTitle>
            <CardDescription>
              Select stones to issue for the selected customer and filters
            </CardDescription>
          </CardHeader>
          <CardContent>
            {availableStones.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No stones available matching the selected criteria
              </p>
            ) : (
              <div className="space-y-4">
                {availableStones.map((stone) => {
                  const isSelected = selectedStones.some(s => s.inventory_id === stone.inventory_id);
                  const selectedStone = selectedStones.find(s => s.inventory_id === stone.inventory_id);

                  return (
                    <div key={stone.inventory_id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleStoneSelection(stone.inventory_id, checked as boolean)}
                      />
                      
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary">
                            {stone.stone_details?.type_name || 'Unknown Type'}
                          </Badge>
                          <Badge variant="outline">
                            {stone.stone_shape?.shape_name || 'Unknown Shape'}
                          </Badge>
                          <Badge variant="outline">
                            {stone.stone_size?.size_name || 'Unknown Size'}
                          </Badge>
                          <Badge variant="outline">
                            {stone.stone_quality?.quality_name || 'Unknown Quality'}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-muted-foreground">
                          Available: {stone.quantity} pcs | Weight: {stone.weight_carats ? stone.weight_carats.toFixed(3) : 'N/A'} ct
                        </div>
                      </div>
                      
                      {isSelected && (
                        <div className="flex items-center space-x-2">
                          <Label htmlFor={`qty-${stone.inventory_id}`} className="text-sm">
                            Qty:
                          </Label>
                          <Input
                            id={`qty-${stone.inventory_id}`}
                            type="number"
                            min="1"
                            max={stone.quantity}
                            value={selectedStone?.quantity || 1}
                            onChange={(e) => updateStoneQuantity(stone.inventory_id, parseInt(e.target.value) || 1)}
                            className="w-20"
                          />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Selected Stones Summary */}
      {selectedStones.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Issue Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Total Stones:</span>
                <span className="font-medium">{totalSelectedQuantity} pcs</span>
              </div>
              <div className="flex justify-between">
                <span>Total Weight:</span>
                <span className="font-medium">{totalSelectedWeight.toFixed(3)} ct</span>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex justify-end space-x-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setSelectedStones([]);
                  form.reset();
                }}
              >
                Clear Selection
              </Button>
              <Button 
                onClick={form.handleSubmit(onSubmit)}
                disabled={isSubmitting || selectedStones.length === 0}
              >
                {isSubmitting ? 'Issuing...' : 'Issue Stones'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
