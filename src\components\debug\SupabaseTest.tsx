'use client';

import { useEffect } from 'react';
import { supabase } from '@/lib/db';

export function SupabaseTest() {
  useEffect(() => {
    const testConnection = async () => {
      console.log('Testing Supabase connection...');
      console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
      
      try {
        const { data, error } = await supabase
          .from('item_type_mast')
          .select('*');
        
        console.log('Connection test result:', {
          success: !error,
          data,
          error
        });
      } catch (err) {
        console.error('Connection test failed:', err);
      }
    };

    testConnection();
  }, []);

  return null;
}
