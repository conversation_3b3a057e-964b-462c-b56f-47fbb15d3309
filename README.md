# JewelPro - Order-Centric Material Loss Tracking System

[![Production Ready](https://img.shields.io/badge/Status-Production%20Ready-brightgreen)](https://github.com/kartikddoshi/JewelPro)
[![Next.js](https://img.shields.io/badge/Next.js-14-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Supabase](https://img.shields.io/badge/Supabase-PostgreSQL-green)](https://supabase.com/)

## 🎯 **Core Business Focus**

A **jewelry manufacturing process management system** specifically designed for **order-based material loss tracking**. Every gram of material loss is tracked by order, worker, and process for comprehensive monthly reporting and business analytics.

### **Primary Business Value**
- **Monthly Loss Reports**: "Show me total metal loss for July 2025"
- **Order-Specific Analysis**: "What was the loss for Order #12345?"
- **Worker Performance**: "Which worker has highest loss on Setting process?"
- **Process Efficiency**: "Which process causes most loss across all orders?"

## ✅ **Production-Ready Features**

### **Order-Centric Workflow**
```
Order #12345 → Filing Process → Worker A → 25.5g Gold Issued
↓
Worker A Returns → 24.8g + 0.5g Dust → 0.2g Loss (0.8% - Acceptable)
↓
Loss Recorded for Order #12345 → Available in Monthly Reports
```

### **Core Components**

1. **🔴 Universal Receipt Form** (`/materials/universal-receipt`)
   - Order-first selection with smart context loading
   - Real-time loss calculation with process-specific thresholds
   - Batch processing for multiple materials
   - Visual feedback with color-coded loss indicators

2. **🔴 Loss Analysis Dashboard** (`/reports/loss-analysis`)
   - Monthly loss reports with trend analysis
   - Order-specific loss tracking
   - Worker performance metrics
   - Process efficiency analysis

3. **🔴 Interactive Weight Tracker**
   - Visual before/after weight comparison
   - Color-coded loss status (green/yellow/red)
   - Process-specific loss thresholds
   - Smart dust collection suggestions

4. **🔴 Material Flow Dashboard** (`/materials/dashboard`)
   - Real-time transaction monitoring
   - Exception highlighting for overdue/high-loss items
   - Quick action center for management tasks

5. **🔴 Mobile Workshop Interface**
   - Touch-optimized for workshop floor use
   - Voice input for notes and descriptions
   - Offline capability with sync when connected

---

## 🛠 **Tech Stack**

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL) with Row Level Security
- **Authentication**: Supabase Auth with role-based access
- **UI Components**: Custom components with Radix UI primitives
- **State Management**: React hooks with context
- **Forms**: React Hook Form with Zod validation

---

## 🚀 **Quick Start**

### Prerequisites
- Node.js 18+
- Supabase account

### Installation

1. **Clone and Install**:
```bash
git clone https://github.com/kartikddoshi/JewelPro.git
cd JewelPro
npm install
```

2. **Environment Setup**:
```bash
cp .env.example .env.local
```

Add your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

3. **Database Setup**:
```bash
npm run db:migrate
npm run gen:types
```

4. **Start Development**:
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000)

---

## 📊 **Key Business Workflows**

### **Material Issue Workflow**
1. Select Order Number (primary key)
2. Choose Process and Worker
3. Issue materials (stones, findings, metals)
4. Track issued weights and quantities

### **Material Receipt Workflow**
1. Select Order Number and Process
2. Enter received weights
3. System calculates loss percentage
4. Collect dust if applicable
5. Loss data recorded for reporting

### **Loss Analysis Workflow**
1. Monthly reports show total loss across all orders
2. Drill down to specific order loss details
3. Analyze worker performance trends
4. Identify process efficiency opportunities

---

## 🏗 **Project Structure**

```
src/
├── app/
│   ├── materials/           # Material management pages
│   │   ├── universal-receipt/   # Order-centric receipt form
│   │   └── dashboard/           # Real-time material flow
│   ├── reports/
│   │   └── loss-analysis/       # Core business reporting
│   └── test-components/         # Component testing
├── components/
│   ├── materials/           # Material management components
│   ├── reports/            # Loss analysis components
│   ├── mobile/             # Workshop-optimized components
│   └── dashboard/          # Management dashboards
├── services/               # Business logic and API calls
└── types/                 # TypeScript definitions
```

---

## 🔐 **Security & Access Control**

- **Role-Based Access**: Admin, Data Entry, Supervisor, Worker
- **Row Level Security**: Customer data segregation enforced at database level
- **Material Segregation**: Customer A's materials never mix with Customer B's
- **Audit Trail**: Complete transaction history for compliance

---

## 📈 **Business Impact**

### **Before Implementation**
- Manual loss tracking with spreadsheets
- No order-specific loss visibility
- Difficult to identify performance issues
- Limited monthly reporting capabilities

### **After Implementation**
- ✅ Real-time loss tracking by order
- ✅ Automated monthly loss reports
- ✅ Worker performance analytics
- ✅ Process optimization insights
- ✅ Customer material segregation
- ✅ Mobile workshop accessibility

---

## 🎯 **Production Deployment**

The system is **production-ready** with:
- ✅ Complete order-centric workflow
- ✅ Real-time loss calculation
- ✅ Mobile-responsive design
- ✅ Offline capability
- ✅ Comprehensive reporting
- ✅ Security and segregation

**Ready for jewelry manufacturing operations!**

---

## 📞 **Support**

For technical support or business questions, please refer to the documentation or contact the development team.

## 📄 **License**

This project is proprietary and confidential.
