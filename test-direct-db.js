const { createClient } = require('@supabase/supabase-js');

// Use the same credentials as in the working test
const supabaseUrl = 'https://vcimuvdnftocekbqrbfy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZjaW11dmRuZnRvY2VrYnFyYmZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUwNTA4NjMsImV4cCI6MjA1MDYyNjg2M30.AeYeyHGK8Ld5QDhVqDEjnBvRK4BpyqpSpnbq843nzf4';

async function testDirectDB() {
  console.log('Testing with anon key (same as API route)...');
  
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  
  try {
    const { data, error } = await supabase
      .from('metal_type_mast')
      .select('*')
      .order('name');
    
    console.log('Direct DB test result:');
    console.log('Data:', data);
    console.log('Error:', error);
    
    if (error) {
      console.log('Error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      });
    }
    
  } catch (err) {
    console.error('Test failed:', err);
  }
}

testDirectDB();
