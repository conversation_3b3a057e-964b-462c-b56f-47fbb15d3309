-- Insert processes according to the defined flow
INSERT INTO process_mast (process_id, description, is_active) VALUES
    (gen_random_uuid(), 'CAD - Model making in Computer-Aided Design', true),
    (gen_random_uuid(), 'CAM - Computer-Aided Manufacturing process', true),
    (gen_random_uuid(), 'CHECK - Quality check after CAM', true),
    (gen_random_uuid(), 'CAST - Metal casting process', true),
    (gen_random_uuid(), 'PARTS-CHK - Verification of all casted parts', true),
    (gen_random_uuid(), 'FILING - Filing and assembly of parts', true),
    (gen_random_uuid(), 'SETTING - Stone setting and rhodium process', true),
    (gen_random_uuid(), 'POLISH - Final polishing process', true),
    (gen_random_uuid(), 'ASSEMBLY - Part of filing process - assembling components', true),
    (gen_random_uuid(), 'RHODIUM - Part of setting process - rhodium plating', true),
    (gen_random_uuid(), 'POLKI - Setting of polki stones', true),
    (gen_random_uuid(), 'FINAL-QC - Final quality check before delivery', true);

-- Check if we need to insert workers
SELECT COUNT(*) FROM worker_mast;

-- If no workers, insert them
INSERT INTO worker_mast (worker_id, name, is_active, is_vendor, shift_start, shift_end) 
SELECT * FROM (VALUES
    -- CAD Workers (2)
    (gen_random_uuid(), 'Rajesh Kumar', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Amit Verma', true, false, '09:00:00'::time, '18:00:00'::time),
    
    -- Filing Workers (3)
    (gen_random_uuid(), 'Suresh Patel', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Mohammed Ali', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Rahul Sharma', true, false, '09:00:00'::time, '18:00:00'::time),
    
    -- Setting Workers (3)
    (gen_random_uuid(), 'Priya Singh', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Vikram Mehta', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Arun Kumar', true, false, '09:00:00'::time, '18:00:00'::time),
    
    -- Polish Workers (2)
    (gen_random_uuid(), 'Deepak Shah', true, false, '09:00:00'::time, '18:00:00'::time),
    (gen_random_uuid(), 'Raj Malhotra', true, false, '09:00:00'::time, '18:00:00'::time),

    -- Vendor Companies (treated as workers)
    (gen_random_uuid(), 'JewelCAM Services', true, true, NULL, NULL),  -- CAM specialist
    (gen_random_uuid(), 'PerfectCast Industries', true, true, NULL, NULL),  -- Casting specialist
    (gen_random_uuid(), 'SuperPolish Co', true, true, NULL, NULL),  -- Polish vendor
    (gen_random_uuid(), 'RhodiumKing Plating', true, true, NULL, NULL)  -- Rhodium specialist
) AS v(worker_id, name, is_active, is_vendor, shift_start, shift_end)
WHERE NOT EXISTS (SELECT 1 FROM worker_mast LIMIT 1);

-- Create temporary tables for skill assignments
CREATE TEMP TABLE IF NOT EXISTS temp_process_ids AS
SELECT process_id, description 
FROM process_mast 
WHERE description LIKE 'CAD%'
   OR description LIKE 'CAM%'
   OR description LIKE 'CAST%'
   OR description LIKE 'FILING%'
   OR description LIKE 'ASSEMBLY%'
   OR description LIKE 'SETTING%'
   OR description LIKE 'RHODIUM%'
   OR description LIKE 'POLKI%'
   OR description LIKE 'POLISH%';

CREATE TEMP TABLE IF NOT EXISTS temp_worker_ids AS
SELECT worker_id, name, is_vendor 
FROM worker_mast;

-- Insert worker skills if none exist
INSERT INTO worker_process_skill (worker_id, process_id, skill_level, efficiency_factor) 
SELECT 
    w.worker_id,
    p.process_id,
    CASE WHEN w.is_vendor THEN 5 ELSE floor(random() * 3 + 3) END::integer as skill_level,
    CASE WHEN w.is_vendor THEN 1.20 ELSE (random() * 0.4 + 0.8) END::decimal(3,2) as efficiency_factor
FROM temp_worker_ids w
CROSS JOIN temp_process_ids p
WHERE 
    -- Only insert if no skills exist for this worker
    NOT EXISTS (
        SELECT 1 
        FROM worker_process_skill wps 
        WHERE wps.worker_id = w.worker_id
    )
    AND (
        -- CAD Workers
        (w.name IN ('Rajesh Kumar', 'Amit Verma') 
         AND p.description LIKE 'CAD%')
        OR
        -- Filing Workers (including Assembly)
        (w.name IN ('Suresh Patel', 'Mohammed Ali', 'Rahul Sharma') 
         AND (p.description LIKE 'FILING%' OR p.description LIKE 'ASSEMBLY%'))
        OR
        -- Setting Workers (including Rhodium and Polki Setting)
        (w.name IN ('Priya Singh', 'Vikram Mehta', 'Arun Kumar') 
         AND (p.description LIKE 'SETTING%' OR p.description LIKE 'RHODIUM%' OR p.description LIKE 'POLKI%'))
        OR
        -- Polish Workers
        (w.name IN ('Deepak Shah', 'Raj Malhotra') 
         AND p.description LIKE 'POLISH%')
        OR
        -- Vendor Companies
        (w.name = 'JewelCAM Services' AND p.description LIKE 'CAM%')
        OR
        (w.name = 'PerfectCast Industries' AND p.description LIKE 'CAST%')
        OR
        (w.name = 'SuperPolish Co' AND p.description LIKE 'POLISH%')
        OR
        (w.name = 'RhodiumKing Plating' AND p.description LIKE 'RHODIUM%')
    );

-- Clean up temporary tables
DROP TABLE IF EXISTS temp_process_ids;
DROP TABLE IF EXISTS temp_worker_ids;

-- Verify Process Data
SELECT description, is_active, created_at
FROM process_mast
ORDER BY description;

-- Verify Worker Data
SELECT 
    name,
    is_vendor,
    shift_start,
    shift_end,
    is_active
FROM worker_mast
ORDER BY is_vendor, name;

-- Verify Process Assignments
WITH ProcessCounts AS (
    SELECT 
        w.name,
        w.is_vendor,
        COUNT(wps.process_id) as process_count,
        MIN(wps.skill_level) as min_skill,
        MAX(wps.skill_level) as max_skill,
        MIN(wps.efficiency_factor) as min_efficiency,
        MAX(wps.efficiency_factor) as max_efficiency
    FROM worker_mast w
    LEFT JOIN worker_process_skill wps ON w.worker_id = wps.worker_id
    GROUP BY w.name, w.is_vendor
)
SELECT 
    name,
    CASE WHEN is_vendor THEN 'Vendor' ELSE 'Worker' END as type,
    process_count as assigned_processes,
    min_skill || '-' || max_skill as skill_range,
    ROUND(min_efficiency, 2) || '-' || ROUND(max_efficiency, 2) as efficiency_range
FROM ProcessCounts
ORDER BY is_vendor, name;

-- Detailed Process Assignments
SELECT 
    w.name,
    CASE WHEN w.is_vendor THEN 'Vendor' ELSE 'Worker' END as type,
    p.description as process,
    wps.skill_level,
    ROUND(wps.efficiency_factor, 2) as efficiency
FROM worker_mast w
JOIN worker_process_skill wps ON w.worker_id = wps.worker_id
JOIN process_mast p ON wps.process_id = p.process_id
ORDER BY w.is_vendor, w.name, p.description;

-- Verify the final data
SELECT 
    w.name,
    w.is_vendor,
    p.description as process,
    wps.skill_level,
    wps.efficiency_factor
FROM worker_mast w
LEFT JOIN worker_process_skill wps ON w.worker_id = wps.worker_id
LEFT JOIN process_mast p ON wps.process_id = p.process_id
ORDER BY w.is_vendor, w.name, p.description;

-- Add table comments
COMMENT ON TABLE process_mast IS 'Master table for jewelry manufacturing processes';
COMMENT ON TABLE worker_mast IS 'Master table for workers and vendors';
COMMENT ON TABLE worker_process_skill IS 'Mapping of worker/vendor skills to processes';

-- Query to check table names
SELECT tablename 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename LIKE '%process%';

-- Clean up duplicate process
DELETE FROM process_mast 
WHERE description = 'Model making in Computer-Aided Design';
