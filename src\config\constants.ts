import { Column, <PERSON>Field } from '@/types/common';
import {
  ItemTypeMaster,
  KaratMaster,
  MetalColorMaster,
  OrderCategoryMaster,
  StyleMaster,
  CustomerMaster,
} from '@/types/masters';

interface MasterConfig<T> {
  title: string;
  columns: Column<T>[];
  fields: FormField[];
}

export const MASTER_CONFIGS = {
  itemType: {
    title: 'Item Types',
    columns: [
      { key: 'description', label: 'Description' },
      { key: 'average_processing_time', label: 'Avg. Processing Time (hrs)' },
      { key: 'suffix', label: 'Order ID Suffix' },
    ] as Column<ItemTypeMaster>[],
    fields: [
      {
        id: 'description',
        label: 'Description',
        type: 'text',
        required: true,
      },
      {
        id: 'average_processing_time',
        label: 'Average Processing Time (hrs)',
        type: 'number',
        required: true,
      },
      {
        id: 'suffix',
        label: 'Order ID Suffix (2 chars)',
        type: 'text',
        required: true,
        pattern: '^[A-Z0-9]{2}$',
        maxLength: 2,
        helpText: 'Two-letter code used in order IDs (e.g., RN for Ring)',
      },
    ],
  } as MasterConfig<ItemTypeMaster>,

  karat: {
    title: 'Karats',
    columns: [
      { key: 'description', label: 'Description' },
      { key: 'purity', label: 'Purity' },
      { key: 'standard_wastage', label: 'Standard Wastage' },
      { key: 'density', label: 'Density (g/cm³)' },
    ] as Column<KaratMaster>[],
    fields: [
      {
        id: 'description',
        label: 'Description',
        type: 'text',
        required: true,
      },
      {
        id: 'purity',
        label: 'Purity',
        type: 'number',
        required: true,
      },
      {
        id: 'standard_wastage',
        label: 'Standard Wastage',
        type: 'number',
        required: true,
      },
      {
        id: 'density',
        label: 'Density (g/cm³)',
        type: 'number',
        required: true,
      },
    ],
  } as MasterConfig<KaratMaster>,

  metalColor: {
    title: 'Metal Colors',
    columns: [
      { key: 'description', label: 'Description' },
      { key: 'processing_complexity_factor', label: 'Processing Complexity Factor' },
    ] as Column<MetalColorMaster>[],
    fields: [
      {
        id: 'description',
        label: 'Description',
        type: 'text',
        required: true,
      },
      {
        id: 'processing_complexity_factor',
        label: 'Processing Complexity Factor',
        type: 'number',
        required: true,
      },
    ],
  } as MasterConfig<MetalColorMaster>,

  orderCategory: {
    title: 'Order Categories',
    columns: [
      { key: 'description', label: 'Description' },
      { key: 'base_processing_time', label: 'Base Processing Time' },
    ] as Column<OrderCategoryMaster>[],
    fields: [
      {
        id: 'description',
        label: 'Description',
        type: 'text',
        required: true,
      },
      {
        id: 'base_processing_time',
        label: 'Base Processing Time',
        type: 'number',
        required: true,
      },
    ],
  } as MasterConfig<OrderCategoryMaster>,

  style: {
    title: 'Styles',
    columns: [
      // { key: 'description', label: 'Description' }, // Removed - Not in schema
      { key: 'item_type_id', label: 'Item Type' },
      { key: 'net_wt', label: 'Net Weight' },
      { key: 'net_wt_kt', label: 'Net Weight Karat' },
      { key: 'estimated_processing_time', label: 'Est. Processing Time' },
    ] as Column<StyleMaster>[],
    fields: [
      // { // Removed - Not in schema
      //   id: 'description',
      //   label: 'Description',
      //   type: 'text',
      //   required: true,
      // },
      {
        id: 'item_type_id',
        label: 'Item Type',
        type: 'select',
        required: true,
        options: [], // This will be populated dynamically
      },
      {
        id: 'net_wt',
        label: 'Net Weight',
        type: 'number',
        required: true,
      },
      {
        id: 'net_wt_kt',
        label: 'Net Weight Karat',
        type: 'select',
        required: true,
        options: [], // Will be populated with karat options dynamically
        helpText: 'Select the karat purity for this style',
      },
      {
        id: 'estimated_processing_time',
        label: 'Estimated Processing Time',
        type: 'number',
        required: true,
      },
      {
        id: 'processing_notes',
        label: 'Processing Notes',
        type: 'text',
        required: false,
      },
    ],
  } as MasterConfig<StyleMaster>,

  customer: {
    title: 'Customers',
    columns: [
      { key: 'description', label: 'Description' } // Corrected label
    ] as Column<CustomerMaster>[],
    fields: [
      {
        id: 'description',
        label: 'Description', // Corrected label
        type: 'text',
        required: true,
        // Removed pattern and maxLength as it's a description field
      }
    ],
  } as MasterConfig<CustomerMaster>,
};
