/**
 * Order-related database queries module
 * Contains all database queries related to orders and their processing
 * @module queries/orders
 */

import { supabase } from '@/lib/db';
import { BaseQueries } from './base';
import { withTransaction, handleError } from '../utils';
import type { Tables } from '../types';
// Corrected Order import path
import { Order } from '@/types/orders'; 

/**
 * Collection of order-related database queries
 * Provides methods for retrieving and manipulating order data
 */
export class OrderQueries extends BaseQueries<'orders'> {
  /**
   * Constructor for OrderQueries
   */
  constructor() {
    super('orders', 'order_id');
  }

  /**
   * Checks if orders have any references in process tracking or history
   * 
   * @param {string[]} orderIds - Array of order IDs to check
   * @returns {Promise<{ order_id: string, process_count: number, history_count: number }[]>}
   * @throws {Error} If query fails
   */
  async checkOrderReferences(orderIds: string[]) {
    // Check process tracking - Fetch raw data
    const { data: processData, error: processError } = await supabase
      .from('process_tracking')
      .select('order_id, status')
      .in('order_id', orderIds);

    if (processError) {
      console.error('Error checking process tracking references', processError);
      handleError(processError);
      throw processError;
    }

    // Check process history - Fetch raw data
    const { data: historyData, error: historyError } = await supabase
      .from('process_history')
      .select('order_id')
      .in('order_id', orderIds);

    if (historyError) {
      console.error('Error checking process history references', historyError);
      handleError(historyError);
      throw historyError;
    }

    // Client-side aggregation
    const processCounts = (processData || []).reduce((acc: Record<string, number>, item) => {
      if (item.order_id) { // Ensure order_id is not null/undefined
        acc[item.order_id] = (acc[item.order_id] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const historyCounts = (historyData || []).reduce((acc: Record<string, number>, item) => {
      if (item.order_id) { // Ensure order_id is not null/undefined
        acc[item.order_id] = (acc[item.order_id] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return orderIds.map(orderId => {
      return {
        order_id: orderId,
        process_count: processCounts[orderId] || 0,
        history_count: historyCounts[orderId] || 0
      };
    });
  }

  /**
   * Deletes multiple orders in a single transaction
   * 
   * @param {string[]} orderIds - Array of order IDs to delete
   * @returns {Promise<void>}
   * @throws {Error} If deletion fails or orders have references
   */
  async bulkDelete(orderIds: string[]) {
    return withTransaction(async () => {
      // Check references first
      const references = await this.checkOrderReferences(orderIds);
      const undeleteableOrders = references.filter(ref => 
        ref.process_count > 0 || ref.history_count > 0
      );

      if (undeleteableOrders.length > 0) {
        const message = undeleteableOrders
          .map(ref => `Order ${ref.order_id}: has ${ref.process_count} processes and ${ref.history_count} history records`)
          .join('\n');
        throw new Error(`Cannot delete orders:\n${message}`);
      }

      const { error } = await supabase
        .from('orders')
        .delete()
        .in('order_id', orderIds);

      if (error) {
        console.error('Error deleting orders', error);
        handleError(error);
        throw error;
      }
    });
  }

  /**
   * Retrieves the process status of an order
   * 
   * @param {string} orderId - ID of the order to retrieve status for
   * @returns {Promise<{ total: number, completed: number, pending: number, in_progress: number, on_hold: number, cancelled: number, can_complete: boolean }>}
   * @throws {Error} If query fails
   */
  async getProcessStatus(orderId: string) {
    const { data, error } = await supabase
      .from('process_tracking')
      .select('status')
      .eq('order_id', orderId);

    if (error) {
      console.error('Error getting process status', error);
      handleError(error);
      throw error;
    }

    // Client-side status counting
    const counts = (data || []).reduce((acc: Record<string, number>, curr: { status: string | null }) => {
      const statusKey = curr.status ?? 'unknown'; // Use nullish coalescing
      acc[statusKey] = (acc[statusKey] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const total = Object.values(counts).reduce((sum: number, count: number) => sum + count, 0);

    return {
      total,
      completed: counts['completed'] || 0,
      pending: counts['pending'] || 0,
      in_progress: counts['in_progress'] || 0,
      on_hold: counts['on_hold'] || 0,
      cancelled: counts['cancelled'] || 0,
      can_complete: counts['completed'] === total && total > 0
    };
  }

  /**
   * Retrieves all orders with their associated details
   * 
   * @returns {Promise<OrderWithDetails[]>}
   * @throws {Error} If query fails
   */
  async getOrders(): Promise<OrderWithDetails[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:customer_id(description),
        third_party_customer:third_party_cust_id(description)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting orders', error);
      handleError(error);
      throw error;
    }
    return (data as OrderWithDetails[] | null) || [];
  }

  /**
   * Completes multiple orders in a single transaction
   * 
   * @param {string[]} orderIds - Array of order IDs to complete
   * @returns {Promise<void>}
   * @throws {Error} If completion fails or orders are not completable
   */
  async bulkComplete(orderIds: string[]) {
    return withTransaction(async () => {
      // Check all orders first
      const statusPromises = orderIds.map(id => this.getProcessStatus(id));
      const statuses = await Promise.all(statusPromises);

      const incompleteOrders = orderIds.filter((id, index) => !statuses[index].can_complete);

      if (incompleteOrders.length > 0) {
        throw new Error(`Cannot complete orders: ${incompleteOrders.join(', ')}. They have incomplete processes.`);
      }

      const { error } = await supabase
        .from('orders')
        .update({ order_status: 'completed' })
        .in('order_id', orderIds);

      if (error) {
        console.error('Error completing orders', error);
        handleError(error);
        throw error;
      }
    });
  }

  /**
   * Retrieves all order IDs from the orders table.
   * @returns {Promise<string[]>} A promise that resolves to an array of all order IDs.
   * @throws {Error} Throws an error if the database query fails.
   */
  async getAllOrderIds(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from(this.tableName)
        .select(this.pkColumn);

      if (error) {
        console.error(`Error fetching all order IDs from ${this.tableName}`, error);
        handleError(error);
        throw error;
      }

      // Explicitly type item to ensure pkColumn access is safe
      return (data || []).map((item: { [key: string]: any }) => item[this.pkColumn]);
    } catch (error) {
      console.error(`[${this.constructor.name}] Error in getAllOrderIds:`, error);
      throw error;
    }
  }

  /**
   * Retrieves all unique worker IDs referenced in the orders table.
   *
   * @returns {Promise<string[]>} A promise that resolves to an array of unique worker IDs.
   * @throws {Error} Throws an error if the database query fails.
   */
  public async getAllReferencedWorkerIds(): Promise<string[]> {
    const { data, error } = await supabase
      .from(this.tableName)
      .select('worker_id', { count: 'exact', head: false })
      .not('worker_id', 'is', null); // Ensure we only get rows where worker_id is not null

    if (error) {
      console.error('Error fetching referenced worker IDs from orders', error);
      handleError(error);
      throw error;
    }

    // Extract unique IDs
    const uniqueIds = [...new Set(data.map(item => item.worker_id).filter(id => id !== null))] as string[];
    return uniqueIds;
  }
}

// Export singleton instance
export const orderQueries = new OrderQueries();

/**
 * Type definition for an order with its associated details
 */
export interface OrderWithDetails extends Order {
  // Explicitly include order_status even though it's in the base Order type
  // This helps TypeScript resolve the type correctly in the store
  order_status: string;
  customer: Pick<Tables['customer_mast']['Row'], 'customer_id' | 'description'> | null;
  third_party_customer: Pick<Tables['third_party_cust_mast']['Row'], 'description'> | null;
}
