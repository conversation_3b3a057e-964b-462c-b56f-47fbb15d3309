import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
// Corrected import path using alias
import { Database } from '@/types/supabase'; 

export async function middleware(req: NextRequest) {
  const res = NextResponse.next();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          res.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          res.cookies.set({ name, value: '', ...options });
        },
      },
    }
  );

  // For testing: Always assume admin session
  const mockAdminSession = {
    user: {
      id: 'mock-admin-id',
      user_metadata: {
        role: 'admin'
      }
    }
  };

  // Comment out actual session check and use mock session
  // const {
  //   data: { session },
  // } = await supabase.auth.getSession();

  const session = mockAdminSession;

  // Bypass auth check for testing
  // if (!session && !req.nextUrl.pathname.startsWith('/auth')) {
  //   const redirectUrl = req.nextUrl.clone();
  //   redirectUrl.pathname = '/auth/login';
  //   redirectUrl.searchParams.set('redirectedFrom', req.nextUrl.pathname);
  //   return NextResponse.redirect(redirectUrl);
  // }

  // Always set admin role for testing
  res.headers.set('x-user-role', 'admin');

  return res;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     * - auth (auth pages)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|auth).*)',
  ],
}
