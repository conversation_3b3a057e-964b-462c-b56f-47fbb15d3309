import { NextResponse } from 'next/server';
import InventoryService from '@/services/InventoryService';
import { handleApiError } from '@/lib/errorHandling';

export async function GET() {
  try {
    const locations = await InventoryService.getLocations();
    return NextResponse.json({ data: locations });
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const newLocation = await InventoryService.createLocation(body);
    return NextResponse.json(newLocation, { status: 201 });
  } catch (error) {
    return handleApiError(error);
  }
}
