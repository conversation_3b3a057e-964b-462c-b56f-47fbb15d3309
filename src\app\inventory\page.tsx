/**
 * Inventory Home Page
 * Main entry point for inventory management features
 * 
 * @module app/inventory
 */

import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Inventory Management',
  description: 'Manage jewelry manufacturing inventory'
};

/**
 * Inventory Home Page Component
 * Provides navigation to inventory management features
 */
export default function InventoryHomePage(): JSX.Element {
  // Define feature cards for better type safety
  const featureCards: Array<{
    title: string;
    description: string;
    href: string;
  }> = [
    {
      title: 'Material Receipt',
      description: 'Record materials received from customers',
      href: '/inventory/material-receipt'
    },
    {
      title: 'Order Allocations',
      description: 'Allocate materials to specific orders',
      href: '/inventory/order-allocations'
    },
    {
      title: 'Process Consumption',
      description: 'Record material consumption in processes',
      href: '/inventory/process-consumption'
    },
    {
      title: 'Master Data',
      description: 'Manage inventory master data',
      href: '/inventory/masters'
    },
    {
      title: 'Loss Reports',
      description: 'Analyze material losses in processes',
      href: '/inventory/reports/loss'
    },
    {
      title: 'Test Page',
      description: 'Simple test page for troubleshooting',
      href: '/inventory/test'
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Inventory Management</h1>
      
      <p className="mb-4 text-gray-600 dark:text-gray-400">
        Select a feature from the sidebar or use the links below:
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        {featureCards.map((card, index) => (
          <Link 
            key={index}
            href={card.href}
            className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <h2 className="text-xl font-semibold mb-2">{card.title}</h2>
            <p className="text-gray-600 dark:text-gray-400">{card.description}</p>
          </Link>
        ))}
      </div>
    </div>
  );
}
