/**
 * CustomerMaterialReceiptList Component
 * List and management interface for customer material receipts
 * 
 * @module components/inventory/receipt
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Plus, FileEdit, Trash2, Eye } from 'lucide-react';
import { CustomerMaterialReceipt } from '@/types/inventory';
import { useToast } from '@/hooks/useToast';
import { formatDate } from '@/lib/utils';

/**
 * CustomerMaterialReceiptList Component
 * Displays and manages customer material receipts
 */
export const CustomerMaterialReceiptList: React.FC = () => {
  const [receipts, setReceipts] = useState<CustomerMaterialReceipt[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { showToast } = useToast();

  useEffect(() => {
    fetchReceipts();
  }, []);

  /**
   * Fetches material receipts from the API
   */
  const fetchReceipts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/inventory/material-receipts');
      
      if (!response.ok) {
        throw new Error('Failed to fetch material receipts');
      }
      
      const data = await response.json();
      setReceipts(data);
      setError(null);
    } catch (err) {
      setError('Error loading material receipts. Please try again later.');
      console.error('Error fetching material receipts:', err);
      showToast({
        title: 'Error',
        description: 'Error fetching material receipts',
        type: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles navigation to create a new material receipt
   */
  const handleCreateNew = () => {
    router.push('/inventory/material-receipt/create');
  };

  /**
   * Handles navigation to view a material receipt
   * @param id - The receipt ID to view
   */
  const handleView = (id: string) => {
    router.push(`/inventory/material-receipt/${id}`);
  };

  /**
   * Handles navigation to edit a material receipt
   * @param id - The receipt ID to edit
   */
  const handleEdit = (id: string) => {
    router.push(`/inventory/material-receipt/${id}/edit`);
  };

  /**
   * Handles deletion of a material receipt
   * @param id - The receipt ID to delete
   */
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this material receipt? This action cannot be undone.')) {
      return;
    }
    
    try {
      const response = await fetch(`/api/inventory/material-receipts?id=${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete material receipt');
      }
      
      await fetchReceipts();
      showToast({
        title: 'Success',
        description: 'Material receipt deleted successfully',
        type: 'success'
      });
    } catch (err) {
      console.error('Error deleting material receipt:', err);
      showToast({
        title: 'Error',
        description: 'Error deleting material receipt',
        type: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Customer Material Receipts</h1>
        <button
          onClick={handleCreateNew}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-5 h-5 mr-2" />
          Create New Receipt
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Receipt ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Order
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Receipt Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {receipts.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No material receipts found
                  </td>
                </tr>
              ) : (
                receipts.map((receipt) => (
                  <tr key={receipt.receipt_id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {receipt.receipt_id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {receipt.customer_id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {receipt.order_id ? receipt.order_id.substring(0, 8) + '...' : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(receipt.receipt_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleView(receipt.receipt_id)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                        title="View Receipt"
                      >
                        <Eye className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleEdit(receipt.receipt_id)}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-4"
                        title="Edit Receipt"
                      >
                        <FileEdit className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(receipt.receipt_id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Delete Receipt"
                      >
                        <Trash2 className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};
