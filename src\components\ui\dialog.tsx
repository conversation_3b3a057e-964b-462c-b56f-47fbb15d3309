"use client";
/**
 * Dialog UI components for modal dialogs
 *
 * Accessible, composable dialog components for displaying modal content.
 *
 * Components:
 * - Dialog: Root component that manages open/close state
 * - DialogTrigger: Button or element to open the dialog
 * - DialogContent: Modal content container
 * - DialogHeader: Header section for title/close
 * - DialogTitle: Title text
 * - DialogFooter: Footer section for actions
 *
 * @example
 * <Dialog>
 *   <DialogTrigger>Open</DialogTrigger>
 *   <DialogContent>
 *     <DialogHeader>
 *       <DialogTitle>Title</DialogTitle>
 *     </DialogHeader>
 *     Body
 *     <DialogFooter>
 *       <button>Close</button>
 *     </DialogFooter>
 *   </DialogContent>
 * </Dialog>
 */
import * as React from 'react';

export interface DialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

export const Dialog: React.FC<DialogProps> = ({ open: controlledOpen, onOpenChange, children }) => {
  const [open, setOpen] = React.useState(false);
  const isControlled = controlledOpen !== undefined;
  const dialogOpen = isControlled ? controlledOpen : open;

  const handleOpenChange = (next: boolean) => {
    if (!isControlled) setOpen(next);
    onOpenChange?.(next);
  };

  return (
    <DialogContext.Provider value={{ open: dialogOpen, setOpen: handleOpenChange }}>
      {children}
    </DialogContext.Provider>
  );
};

interface DialogContextType {
  open: boolean;
  setOpen: (open: boolean) => void;
}
const DialogContext = React.createContext<DialogContextType | undefined>(undefined);

/**
 * DialogTrigger - element to open the dialog
 */
export const DialogTrigger: React.FC<React.HTMLAttributes<HTMLElement>> = ({ children, ...props }) => {
  const ctx = React.useContext(DialogContext);
  if (!ctx) throw new Error('DialogTrigger must be used within a Dialog');
  return (
    <button type="button" onClick={() => ctx.setOpen(true)} {...props}>
      {children}
    </button>
  );
};

/**
 * DialogContent - modal content container
 */
export const DialogContent: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ children, ...props }) => {
  const ctx = React.useContext(DialogContext);
  if (!ctx) throw new Error('DialogContent must be used within a Dialog');
  if (!ctx.open) return null;
  return (
    <div
      role="dialog"
      aria-modal="true"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
      {...props}
    >
      <div className="bg-white rounded-lg shadow-lg max-w-lg w-full p-6 relative">
        {children}
        <button
          aria-label="Close dialog"
          className="absolute top-2 right-2 text-gray-400 hover:text-gray-900"
          onClick={() => ctx.setOpen(false)}
        >
          ×
        </button>
      </div>
    </div>
  );
};

/**
 * DialogHeader - header section for dialog
 */
export const DialogHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ children, ...props }) => (
  <div className="mb-4" {...props}>{children}</div>
);

/**
 * DialogTitle - title text for dialog
 */
export const DialogTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement>> = ({ children, ...props }) => (
  <h2 className="text-lg font-semibold" {...props}>{children}</h2>
);

/**
 * DialogFooter - footer section for dialog actions
 */
export const DialogFooter: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({ children, ...props }) => (
  <div className="mt-6 flex justify-end gap-2" {...props}>{children}</div>
);
