'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
// Placeholder Holiday interface - to be replaced with actual DB type when available
interface Holiday {
  holiday_id: string;
  holiday_date: string;
  holiday_name: string;
  description?: string; // Added based on form usage
  holiday_type: string;
  is_recurring: boolean;
  working_hours?: string; // Added based on form usage
  recurring_rule?: { // Added based on form usage
    frequency?: string;
    interval?: number;
  };
  created_at: string;
  updated_at: string;
}

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';

interface HolidayFormData extends Omit<Holiday, 'holiday_id' | 'created_at' | 'updated_at'> {}

export function HolidayForm() {
    const [loading, setLoading] = useState(false);
    const { toast } = useToast();

    const { register, handleSubmit, watch, reset, formState: { errors } } = useForm<HolidayFormData>();
    const holidayType = watch('holiday_type');
    const isRecurring = watch('is_recurring');

    const onSubmit = async (data: HolidayFormData) => {
        setLoading(true);
        try {
            const response = await fetch('/api/holidays', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) throw new Error('Failed to save holiday');

            toast({
                title: 'Success',
                description: 'Holiday saved successfully',
                variant: 'default'
            });
            reset();
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to save holiday',
                variant: 'destructive'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
                <label className="block text-sm font-medium mb-2">Date</label>
                <Input
                    type="date"
                    {...register('holiday_date', { required: 'Date is required' })}
                    className="w-full"
                />
                {errors.holiday_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.holiday_date.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <Input
                    type="text"
                    {...register('description', { required: 'Description is required' })}
                    className="w-full"
                />
                {errors.description && (
                    <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
                )}
            </div>

            <div>
                <label className="block text-sm font-medium mb-2">Holiday Type</label>
                <Select
                    {...register('holiday_type', { required: 'Holiday type is required' })}
                    className="w-full"
                >
                    <option value="">Select Type</option>
                    <option value="FULL_DAY">Full Day</option>
                    <option value="HALF_DAY">Half Day</option>
                    <option value="UNSCHEDULED">Unscheduled</option>
                </Select>
                {errors.holiday_type && (
                    <p className="text-red-500 text-sm mt-1">{errors.holiday_type.message}</p>
                )}
            </div>

            {holidayType === 'HALF_DAY' && (
                <div>
                    <label className="block text-sm font-medium mb-2">Working Hours</label>
                    <Input
                        type="number"
                        step="0.5"
                        {...register('working_hours', {
                            required: 'Working hours required for half day',
                            min: { value: 0.5, message: 'Minimum 0.5 hours' },
                            max: { value: 6, message: 'Maximum 6 hours' }
                        })}
                        className="w-full"
                    />
                    {errors.working_hours && (
                        <p className="text-red-500 text-sm mt-1">{errors.working_hours.message}</p>
                    )}
                </div>
            )}

            <div>
                <label className="flex items-center space-x-2">
                    <input
                        type="checkbox"
                        {...register('is_recurring')}
                        className="form-checkbox"
                    />
                    <span className="text-sm font-medium">Recurring Holiday</span>
                </label>
            </div>

            {isRecurring && (
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-2">Frequency</label>
                        <Select
                            {...register('recurring_rule.frequency', {
                                required: 'Frequency is required for recurring holidays'
                            })}
                            className="w-full"
                        >
                            <option value="">Select Frequency</option>
                            <option value="YEARLY">Yearly</option>
                            <option value="MONTHLY">Monthly</option>
                            <option value="WEEKLY">Weekly</option>
                        </Select>
                        {errors.recurring_rule?.frequency && (
                            <p className="text-red-500 text-sm mt-1">
                                {errors.recurring_rule.frequency.message}
                            </p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium mb-2">Interval</label>
                        <Input
                            type="number"
                            {...register('recurring_rule.interval', {
                                required: 'Interval is required for recurring holidays',
                                min: { value: 1, message: 'Minimum interval is 1' }
                            })}
                            className="w-full"
                        />
                        {errors.recurring_rule?.interval && (
                            <p className="text-red-500 text-sm mt-1">
                                {errors.recurring_rule.interval.message}
                            </p>
                        )}
                    </div>
                </div>
            )}

            <Button
                type="submit"
                disabled={loading}
                className="w-full"
            >
                {loading ? 'Saving...' : 'Save Holiday'}
            </Button>
        </form>
    );
}
