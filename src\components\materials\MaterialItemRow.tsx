/**
 * @module components/materials/MaterialItemRow
 * @description Individual material item row with inline editing and real-time loss calculation
 */

'use client';

import React, { useState, useEffect } from 'react';
import { TableRow, TableCell } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { InteractiveWeightTracker } from './InteractiveWeightTracker';
import {
  GemIcon,
  CircleIcon,
  ScaleIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  EditIcon,
  SaveIcon,
  XIcon,
  ZoomInIcon
} from 'lucide-react';

interface MaterialItem {
  id: string;
  type: 'stone' | 'finding' | 'metal';
  description: string;
  issuedWeight: number;
  issuedQuantity?: number;
  receivedWeight: number;
  receivedQuantity?: number;
  dustCollected: number;
  lossPercentage: number;
  status: 'pending' | 'completed' | 'flagged';
  originalData: any;
}

interface MaterialItemRowProps {
  item: MaterialItem;
  isSelected: boolean;
  onSelectionChange: (selected: boolean) => void;
  onItemUpdate: (updatedItem: MaterialItem) => void;
}

export function MaterialItemRow({ 
  item, 
  isSelected, 
  onSelectionChange, 
  onItemUpdate 
}: MaterialItemRowProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedItem, setEditedItem] = useState<MaterialItem>(item);

  // Update edited item when prop changes
  useEffect(() => {
    setEditedItem(item);
  }, [item]);

  // Calculate loss percentage when weights change
  useEffect(() => {
    if (editedItem.issuedWeight > 0) {
      const lossPercentage = ((editedItem.issuedWeight - editedItem.receivedWeight) / editedItem.issuedWeight) * 100;
      const newStatus = lossPercentage > 5 ? 'flagged' : lossPercentage < 0 ? 'flagged' : 'completed';
      
      setEditedItem(prev => ({
        ...prev,
        lossPercentage: Math.max(0, lossPercentage),
        status: newStatus
      }));
    }
  }, [editedItem.receivedWeight, editedItem.issuedWeight]);

  const handleSave = () => {
    onItemUpdate(editedItem);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedItem(item);
    setIsEditing(false);
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'stone':
        return <GemIcon className="w-4 h-4" />;
      case 'finding':
        return <CircleIcon className="w-4 h-4" />;
      case 'metal':
        return <ScaleIcon className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'stone':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'finding':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'metal':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusBadge = (status: string, lossPercentage: number) => {
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <CheckCircleIcon className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case 'flagged':
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            <AlertTriangleIcon className="w-3 h-3 mr-1" />
            {lossPercentage > 5 ? 'High Loss' : 'Flagged'}
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            Pending
          </Badge>
        );
    }
  };

  const getLossColor = (lossPercentage: number) => {
    if (lossPercentage < 0) return 'text-red-600 font-bold'; // Negative loss (gain)
    if (lossPercentage <= 2) return 'text-green-600'; // Good
    if (lossPercentage <= 5) return 'text-yellow-600'; // Acceptable
    return 'text-red-600 font-bold'; // Excessive
  };

  return (
    <TableRow className={isSelected ? 'bg-blue-50 dark:bg-blue-950' : ''}>
      {/* Selection Checkbox */}
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={onSelectionChange}
        />
      </TableCell>

      {/* Type */}
      <TableCell>
        <Badge className={getTypeBadgeColor(item.type)}>
          {getTypeIcon(item.type)}
          <span className="ml-1 capitalize">{item.type}</span>
        </Badge>
      </TableCell>

      {/* Description */}
      <TableCell>
        <div className="max-w-xs">
          <div className="font-medium text-sm">{item.description}</div>
        </div>
      </TableCell>

      {/* Issued Weight/Quantity */}
      <TableCell>
        <div className="text-sm">
          <div className="font-medium">{item.issuedWeight.toFixed(3)}g</div>
          {item.issuedQuantity && (
            <div className="text-gray-500">{item.issuedQuantity} pcs</div>
          )}
        </div>
      </TableCell>

      {/* Received Weight/Quantity */}
      <TableCell>
        {isEditing ? (
          <div className="space-y-2">
            <Input
              type="number"
              step="0.001"
              value={editedItem.receivedWeight}
              onChange={(e) => setEditedItem(prev => ({
                ...prev,
                receivedWeight: parseFloat(e.target.value) || 0
              }))}
              className="w-20 text-sm"
              placeholder="Weight"
            />
            {item.issuedQuantity && (
              <Input
                type="number"
                value={editedItem.receivedQuantity || 0}
                onChange={(e) => setEditedItem(prev => ({
                  ...prev,
                  receivedQuantity: parseInt(e.target.value) || 0
                }))}
                className="w-20 text-sm"
                placeholder="Qty"
              />
            )}
          </div>
        ) : (
          <div className="text-sm">
            <div className="font-medium">{editedItem.receivedWeight.toFixed(3)}g</div>
            {editedItem.receivedQuantity && (
              <div className="text-gray-500">{editedItem.receivedQuantity} pcs</div>
            )}
          </div>
        )}
      </TableCell>

      {/* Dust Collected */}
      <TableCell>
        {isEditing ? (
          <Input
            type="number"
            step="0.001"
            value={editedItem.dustCollected}
            onChange={(e) => setEditedItem(prev => ({
              ...prev,
              dustCollected: parseFloat(e.target.value) || 0
            }))}
            className="w-20 text-sm"
            placeholder="Dust"
          />
        ) : (
          <div className="text-sm">
            {editedItem.dustCollected > 0 ? `${editedItem.dustCollected.toFixed(3)}g` : '-'}
          </div>
        )}
      </TableCell>

      {/* Loss Percentage */}
      <TableCell>
        <div className={`text-sm font-medium ${getLossColor(editedItem.lossPercentage)}`}>
          {editedItem.lossPercentage.toFixed(2)}%
        </div>
      </TableCell>

      {/* Status */}
      <TableCell>
        {getStatusBadge(editedItem.status, editedItem.lossPercentage)}
      </TableCell>

      {/* Actions */}
      <TableCell>
        {isEditing ? (
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleSave}
              className="h-8 w-8 p-0"
            >
              <SaveIcon className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCancel}
              className="h-8 w-8 p-0"
            >
              <XIcon className="w-4 h-4" />
            </Button>
          </div>
        ) : (
          <div className="flex items-center gap-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsEditing(true)}
              className="h-8 w-8 p-0"
            >
              <EditIcon className="w-4 h-4" />
            </Button>

            {/* Weight Tracker Dialog */}
            <Dialog>
              <DialogTrigger>
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-8 w-8 p-0"
                  title="Advanced Weight Tracker"
                >
                  <ZoomInIcon className="w-4 h-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Advanced Weight Tracker - {item.description}</DialogTitle>
                </DialogHeader>
                <InteractiveWeightTracker
                  issuedWeight={item.issuedWeight}
                  receivedWeight={editedItem.receivedWeight}
                  dustCollected={editedItem.dustCollected}
                  processId="default" // TODO: Get from context
                  materialType={item.type}
                  onWeightChange={(weight) => {
                    setEditedItem(prev => ({ ...prev, receivedWeight: weight }));
                  }}
                  onDustChange={(dust) => {
                    setEditedItem(prev => ({ ...prev, dustCollected: dust }));
                  }}
                />
              </DialogContent>
            </Dialog>
          </div>
        )}
      </TableCell>
    </TableRow>
  );
}
