/**
 * Findings Inventory Service
 * 
 * Handles polki and findings-specific operations including composite stone tracking
 */

import { supabase } from '@/lib/db';
import { Database } from '@/types/db';
import { issueMaterialToWorker, receiveMaterialFromWorker } from './materialTransactionService';

type FindingsMast = Database['public']['Tables']['findings_mast']['Row'];
type FindingStoneDetails = Database['public']['Tables']['finding_stone_details']['Row'];

export interface FindingsIssueRequest {
  customer_id: string;
  order_id: string;
  finding_id: string;
  worker_id: string;
  process_id: string;
  issued_by: string;
  notes?: string;
}

export interface FindingsReceiptRequest {
  transaction_id: string;
  finding_id: string;
  received_gross_weight_grams: number;
  received_net_weight_grams: number;
  condition: 'good' | 'damaged' | 'lost';
  stone_losses?: {
    stone_detail_id: string;
    quantity_lost: number;
    loss_reason: string;
  }[];
  received_by: string;
  notes?: string;
}

/**
 * Issue findings to worker for processing
 */
export async function issueFindings(request: FindingsIssueRequest): Promise<{
  transaction_id: string;
  finding_issued: FindingsMast & { stone_details: FindingStoneDetails[] };
}> {
  try {
    // 1. Get finding details with stone composition
    const finding = await getFindingWithStoneDetails(request.finding_id);
    
    if (!finding) {
      throw new Error('Finding not found');
    }

    if (finding.customer_id !== request.customer_id) {
      throw new Error('Finding does not belong to specified customer');
    }

    if (finding.status !== 'available') {
      throw new Error('Finding is not available for issue');
    }

    // 2. Create material transaction record
    const materialTransaction = await issueMaterialToWorker({
      order_id: request.order_id,
      worker_id: request.worker_id,
      process_id: request.process_id,
      customer_id: request.customer_id,
      material_type: 'finding',
      issued_by: request.issued_by,
      notes: request.notes
    });

    // 3. Update finding status
    const { error: updateError } = await supabase
      .from('findings_mast')
      .update({
        status: 'issued',
        updated_at: new Date().toISOString()
      })
      .eq('finding_id', request.finding_id);

    if (updateError) throw updateError;

    // 4. Create finding transaction detail
    await supabase.from('finding_transaction_details').insert({
      transaction_id: materialTransaction.transaction_id,
      finding_id: request.finding_id,
      weight_issued_grams: finding.gross_weight_grams,
      quantity_issued: 1, // Findings are typically issued as single units
      created_at: new Date().toISOString()
    });

    return {
      transaction_id: materialTransaction.transaction_id!,
      finding_issued: finding
    };

  } catch (error) {
    console.error('Error issuing findings:', error);
    throw error;
  }
}

/**
 * Receive findings back from worker
 */
export async function receiveFindings(request: FindingsReceiptRequest): Promise<void> {
  try {
    // 1. Update finding transaction details
    const { error: updateError } = await supabase
      .from('finding_transaction_details')
      .update({
        received_gross_weight_grams: request.received_gross_weight_grams,
        received_net_weight_grams: request.received_net_weight_grams,
        return_condition: request.condition,
        return_date: new Date().toISOString(),
        return_notes: request.notes
      })
      .eq('transaction_id', request.transaction_id)
      .eq('finding_id', request.finding_id);

    if (updateError) throw updateError;

    // 2. Calculate weight loss
    const { data: issuedDetails, error: fetchError } = await supabase
      .from('finding_transaction_details')
      .select('*')
      .eq('transaction_id', request.transaction_id)
      .eq('finding_id', request.finding_id)
      .single();

    if (fetchError) throw fetchError;

    const grossWeightLoss = (issuedDetails.issued_gross_weight_grams || 0) - request.received_gross_weight_grams;
    const netWeightLoss = (issuedDetails.issued_net_weight_grams || 0) - request.received_net_weight_grams;

    // 3. Handle stone losses if any
    if (request.stone_losses && request.stone_losses.length > 0) {
      for (const stoneLoss of request.stone_losses) {
        await supabase.from('finding_stone_loss_log').insert({
          transaction_id: request.transaction_id,
          stone_detail_id: stoneLoss.stone_detail_id,
          quantity_lost: stoneLoss.quantity_lost,
          loss_reason: stoneLoss.loss_reason,
          loss_date: new Date().toISOString()
        });
      }
    }

    // 4. Update finding status based on condition
    if (request.condition === 'good') {
      await supabase
        .from('findings_mast')
        .update({
          gross_weight_grams: request.received_gross_weight_grams,
          net_weight_grams: request.received_net_weight_grams,
          status: 'available',
          updated_at: new Date().toISOString()
        })
        .eq('finding_id', request.finding_id);
    } else if (request.condition === 'lost') {
      await supabase
        .from('findings_mast')
        .update({
          status: 'lost',
          updated_at: new Date().toISOString()
        })
        .eq('finding_id', request.finding_id);
    }

    // 5. Update main material transaction
    await receiveMaterialFromWorker({
      transaction_id: request.transaction_id,
      received_weight_grams: request.received_gross_weight_grams,
      loss_percentage: grossWeightLoss > 0 ? (grossWeightLoss / (issuedDetails.issued_gross_weight_grams || 1)) * 100 : 0,
      received_by: request.received_by,
      notes: request.notes
    });

  } catch (error) {
    console.error('Error receiving findings:', error);
    throw error;
  }
}

/**
 * Get finding with stone details
 */
export async function getFindingWithStoneDetails(
  findingId: string
): Promise<(FindingsMast & { stone_details: FindingStoneDetails[] }) | null> {
  try {
    const { data, error } = await supabase
      .from('findings_mast')
      .select(`
        *,
        stone_details:finding_stone_details(*)
      `)
      .eq('finding_id', findingId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching finding with stone details:', error);
    throw error;
  }
}

/**
 * Get available findings by customer
 */
export async function getAvailableFindings({
  customer_id,
  finding_type,
  min_gross_weight,
  max_gross_weight
}: {
  customer_id: string;
  finding_type?: string;
  min_gross_weight?: number;
  max_gross_weight?: number;
}): Promise<(FindingsMast & { stone_details: FindingStoneDetails[] })[]> {
  try {
    let query = supabase
      .from('findings_mast')
      .select(`
        *,
        stone_details:finding_stone_details(
          *,
          stone_type:stone_type_mast(type_name),
          stone_shape:stone_shape_mast(shape_name),
          stone_size:stone_size_mast(size_name),
          stone_quality:stone_quality_mast(quality_name)
        )
      `)
      .eq('customer_id', customer_id)
      .eq('status', 'available');

    if (finding_type) query = query.eq('finding_type', finding_type);
    if (min_gross_weight) query = query.gte('gross_weight_grams', min_gross_weight);
    if (max_gross_weight) query = query.lte('gross_weight_grams', max_gross_weight);

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching available findings:', error);
    throw error;
  }
}

/**
 * Get findings inventory by customer
 */
export async function getFindingsInventoryByCustomer(customerId: string) {
  try {
    const { data, error } = await supabase
      .from('findings_mast')
      .select(`
        *,
        stone_details:finding_stone_details(
          quantity,
          stone_type:stone_type_mast(type_name),
          stone_shape:stone_shape_mast(shape_name),
          stone_size:stone_size_mast(size_name),
          stone_quality:stone_quality_mast(quality_name)
        )
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching findings inventory:', error);
    throw error;
  }
}

/**
 * Get finding transaction history
 */
export async function getFindingTransactionHistory(findingId: string) {
  try {
    const { data, error } = await supabase
      .from('finding_transaction_details')
      .select(`
        *,
        transaction:material_transactions(
          transaction_date,
          worker:workers_mast(worker_name),
          process:process_mast(process_name)
        )
      `)
      .eq('finding_id', findingId)
      .order('issue_date', { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching finding transaction history:', error);
    throw error;
  }
}

/**
 * Create new finding from received materials
 */
export async function createFinding({
  customer_id,
  finding_type,
  gross_weight_grams,
  net_weight_grams,
  foil_weight_grams,
  description,
  stone_details
}: {
  customer_id: string;
  finding_type: string;
  gross_weight_grams: number;
  net_weight_grams: number;
  foil_weight_grams?: number;
  description?: string;
  stone_details: {
    stone_type_id: string;
    stone_shape_id: string;
    stone_size_id: string;
    stone_quality_id: string;
    quantity: number;
  }[];
}): Promise<FindingsMast> {
  try {
    // 1. Create finding record
    const { data: finding, error: findingError } = await supabase
      .from('findings_mast')
      .insert({
        customer_id,
        finding_type,
        gross_weight_grams,
        net_weight_grams,
        foil_weight_grams,
        description,
        status: 'available',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (findingError) throw findingError;

    // 2. Create stone detail records
    const stoneDetailInserts = stone_details.map(stone => ({
      finding_id: finding.finding_id,
      stone_type_id: stone.stone_type_id,
      stone_shape_id: stone.stone_shape_id,
      stone_size_id: stone.stone_size_id,
      stone_quality_id: stone.stone_quality_id,
      quantity: stone.quantity
    }));

    const { error: stoneError } = await supabase
      .from('finding_stone_details')
      .insert(stoneDetailInserts);

    if (stoneError) throw stoneError;

    return finding;
  } catch (error) {
    console.error('Error creating finding:', error);
    throw error;
  }
}

// Aliases for component compatibility
export const issueFinding = issueFindings;
export const receiveFinding = receiveFindings;

/**
 * Get issued findings for receipt
 */
export async function getIssuedFindings(filters: {
  customer_id?: string;
  order_id?: string;
  worker_id?: string;
  process_id?: string;
}) {
  try {
    let query = supabase
      .from('finding_transaction_details')
      .select(`
        *,
        finding:findings_mast(*),
        transaction:material_transactions(
          transaction_id,
          order_id,
          worker_id,
          process_id,
          customer_id,
          status,
          worker:worker_mast(worker_name),
          process:process_mast(name)
        )
      `)
      .is('received_gross_weight_grams', null); // Only get findings that haven't been received yet

    // Apply filters through the material_transactions join
    if (filters.customer_id) {
      query = query.eq('transaction.customer_id', filters.customer_id);
    }
    if (filters.order_id) {
      query = query.eq('transaction.order_id', filters.order_id);
    }
    if (filters.worker_id) {
      query = query.eq('transaction.worker_id', filters.worker_id);
    }
    if (filters.process_id) {
      query = query.eq('transaction.process_id', filters.process_id);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching issued findings:', error);
    throw error;
  }
}
