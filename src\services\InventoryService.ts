import db from '@/db/inventory';
import { InventoryLocation, MetalTransaction } from '@/types/inventory';
import { handleApiError } from '@/lib/errorHandling';

class InventoryService {
  static async getLocations(): Promise<InventoryLocation[]> {
    try {
      return await db.getLocations();
    } catch (error) {
      handleApiError(error, 'Failed to fetch locations');
      throw error;
    }
  }

  static async createLocation(locationData: Omit<InventoryLocation, 'location_id'>): Promise<InventoryLocation> {
    try {
      return await db.createLocation(locationData);
    } catch (error) {
      handleApiError(error, 'Failed to create location');
      throw error;
    }
  }

  static async createTransaction(transactionData: Omit<MetalTransaction, 'transaction_id'>): Promise<MetalTransaction> {
    try {
      return await db.createTransaction(transactionData);
    } catch (error) {
      handleApiError(error, 'Failed to create transaction');
      throw error;
    }
  }
}

export default InventoryService;
