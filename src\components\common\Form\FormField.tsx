'use client';

import React from 'react';
import { UseFormRegister } from 'react-hook-form';

interface FormFieldProps {
  label: string;
  name?: string;
  type?: string;
  required?: boolean;
  register?: UseFormRegister<any>;
  error?: string;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  children?: React.ReactNode;
}

export const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  required = false,
  register,
  error,
  placeholder,
  className = '',
  disabled = false,
  children,
}) => {
  return (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children ? (
        children
      ) : name && register ? (
        <input
          type={type}
          {...register(name)}
          placeholder={placeholder}
          disabled={disabled}
          className={`
            mt-1
            block
            w-full
            rounded-md
            border-gray-300
            shadow-sm
            focus:border-indigo-500
            focus:ring-indigo-500
            sm:text-sm
            ${error ? 'border-red-500' : ''}
            ${disabled ? 'bg-gray-100' : ''}
            ${className}
            dark:bg-gray-800
            dark:border-gray-600
            dark:text-white
          `}
        />
      ) : null}
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};
