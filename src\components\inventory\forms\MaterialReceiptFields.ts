/**
 * Form field definitions for Material Receipt forms
 * Contains field validation and configuration for inventory receipt operations
 * 
 * @module components/inventory/forms/MaterialReceiptFields
 */

import { FormField } from '@/types/common';

/**
 * Form field definitions for Customer Material Receipt
 * Contains field validation and configuration
 */
export const customerMaterialReceiptFields: Record<string, FormField> = {
  customer_id: {
    id: 'customer_id',
    label: 'Customer',
    type: 'select',
    required: true,
    options: [], // Populated from customers
    placeholder: 'Select customer'
  },
  order_id: {
    id: 'order_id',
    label: 'Order',
    type: 'select',
    required: false,
    options: [], // Populated from orders
    placeholder: 'Select order (optional)'
  },
  receipt_date: {
    id: 'receipt_date',
    label: 'Receipt Date',
    type: 'date',
    required: true,
    placeholder: 'Select receipt date'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 500,
    placeholder: 'Enter additional notes about this receipt'
  }
};

/**
 * Form field definitions for Material Receipt Items
 * For use in adding metal items to a receipt
 */
export const materialReceiptMetalFields: Record<string, FormField> = {
  metal_type_id: {
    id: 'metal_type_id',
    label: 'Metal Type',
    type: 'select',
    required: true,
    options: [], // Populated from metal_type_mast
    placeholder: 'Select metal type'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  purity: {
    id: 'purity',
    label: 'Purity',
    type: 'number',
    required: true,
    min: 1,
    max: 100,
    step: 0.01,
    placeholder: 'Enter purity percentage'
  },
  add_to_pool: {
    id: 'add_to_pool',
    label: 'Add to Metal Pool',
    type: 'checkbox',
    required: false,
    defaultValue: true
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes about this metal item'
  }
};

/**
 * Form field definitions for Material Receipt Diamond Items
 */
export const materialReceiptDiamondFields: Record<string, FormField> = {
  diamond_cut_id: {
    id: 'diamond_cut_id',
    label: 'Diamond Cut',
    type: 'select',
    required: true,
    options: [], // Populated from diamond_cut_mast
    placeholder: 'Select diamond cut'
  },
  stone_shape_id: {
    id: 'stone_shape_id',
    label: 'Stone Shape',
    type: 'select',
    required: true,
    options: [], // Populated from stone_shape_mast
    placeholder: 'Select stone shape'
  },
  size_mm: {
    id: 'size_mm',
    label: 'Size (mm)',
    type: 'number',
    required: false,
    min: 0.01,
    step: 0.01,
    placeholder: 'Enter size in mm'
  },
  quantity: {
    id: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    min: 1,
    step: 1,
    placeholder: 'Enter quantity'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes about these diamonds'
  }
};

/**
 * Form field definitions for Material Receipt Stone Items
 */
export const materialReceiptStoneFields: Record<string, FormField> = {
  stone_type_id: {
    id: 'stone_type_id',
    label: 'Stone Type',
    type: 'select',
    required: true,
    options: [], // Populated from stone_type_mast
    placeholder: 'Select stone type'
  },
  stone_shape_id: {
    id: 'stone_shape_id',
    label: 'Stone Shape',
    type: 'select',
    required: true,
    options: [], // Populated from stone_shape_mast
    placeholder: 'Select stone shape'
  },
  size_mm: {
    id: 'size_mm',
    label: 'Size (mm)',
    type: 'number',
    required: false,
    min: 0.01,
    step: 0.01,
    placeholder: 'Enter size in mm'
  },
  quantity: {
    id: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    min: 1,
    step: 0.1,
    placeholder: 'Enter quantity'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes about these stones'
  }
};

/**
 * Form field definitions for Material Receipt Polki Items
 */
export const materialReceiptPolkiFields: Record<string, FormField> = {
  polki_size_id: {
    id: 'polki_size_id',
    label: 'Polki Size',
    type: 'select',
    required: true,
    options: [], // Populated from polki_size_mast
    placeholder: 'Select polki size'
  },
  quantity: {
    id: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    min: 1,
    step: 1,
    placeholder: 'Enter quantity'
  },
  weight: {
    id: 'weight',
    label: 'Weight',
    type: 'number',
    required: true,
    min: 0.001,
    step: 0.001,
    placeholder: 'Enter weight'
  },
  uom_id: {
    id: 'uom_id',
    label: 'Unit of Measure',
    type: 'select',
    required: true,
    options: [], // Populated from uom_mast
    placeholder: 'Select unit of measure'
  },
  notes: {
    id: 'notes',
    label: 'Notes',
    type: 'textarea',
    required: false,
    maxLength: 200,
    placeholder: 'Enter notes about these polki stones'
  }
};
