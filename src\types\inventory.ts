// Base entity interface for common fields
interface BaseEntity {
  created_at: string;
  updated_at: string;
}

// Legacy inventory interfaces (keeping for backward compatibility)
export interface InventoryLocation extends BaseEntity {
  location_id: string;
  location_name: string;
  location_type: 'MAIN_SAFE' | 'WORKSHOP' | 'VENDOR' | 'CUSTOMER_SITE';
  is_active: boolean;
  parent_location_id?: string;
}

export interface MetalTransaction extends BaseEntity {
  transaction_id: string;
  transaction_type: 'RECEIPT' | 'TRANSFER' | 'ADJUSTMENT';
  from_location_id?: string;
  to_location_id?: string;
  karat_id: string;
  gold_colour_id?: string;
  weight_in_grams: number;
  reference_number?: string;
  notes?: string;
  created_by: string;
  order_id?: string;
}

export interface MetalBalance extends BaseEntity {
  balance_id: string;
  location_id: string;
  karat_id: string;
  gold_colour_id?: string;
  current_weight: number;
  last_updated: string;
}

// New inventory management system interfaces

// Master Tables
export interface MetalTypeMast extends BaseEntity {
  metal_type_id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface StoneTypeMast extends BaseEntity {
  stone_type_id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface StoneShapeMast extends BaseEntity {
  shape_id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface StoneSizeMast extends BaseEntity {
  stone_size_id: string;
  size_name: string;
  description?: string;
  is_active: boolean;
}

export interface DiamondCutMast extends BaseEntity {
  cut_id: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface PolkiSizeMast extends BaseEntity {
  size_id: string;
  size_name: string;
  min_size?: number;
  max_size?: number;
  description?: string;
  is_active: boolean;
}

export interface UomMast extends BaseEntity {
  uom_id: string;
  code: string;
  name: string;
  description?: string;
  is_active: boolean;
}

export interface MetalTransactionTypeMast extends BaseEntity {
  type_id: string;
  code: string;
  name: string;
  description?: string;
  affects_pool: boolean;
  is_active: boolean;
}

export interface ExternalProcessorMast extends BaseEntity {
  processor_id: string;
  name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  is_active: boolean;
}

// Transaction Tables
export interface CustomerMaterialReceipt extends BaseEntity {
  receipt_id: string;
  customer_id: string;
  order_id?: string;
  receipt_date: string;
  notes?: string;
}

export interface MetalPool extends BaseEntity {
  pool_id: string;
  metal_type_id: string;
  karat_id: string;
  initial_weight: number;
  current_weight: number;
  customer_id: string;
  uom_id: string;
  is_active: boolean;

  karat?: {
    purity?: number;
  };
  metal_type?: {
    name?: string;
  };
  uom?: {
    code?: string;
  };
}

export interface MetalTransformation extends BaseEntity {
  transformation_id: string;
  source_pool_id: string;
  target_pool_id: string;
  source_weight: number;
  target_weight: number;
  transformation_date: string;
  process_id?: string;
  notes?: string;
  created_by: string;
}

export interface EnhancedMetalTransaction extends BaseEntity {
  transaction_id: string;
  transaction_type_id: string;
  pool_id: string;
  related_transaction_id?: string;
  order_id?: string;
  process_id?: string;
  external_entity_id?: string;
  weight: number;
  fine_weight?: number;
  uom_id: string;
  transaction_date: string;
  notes?: string;
  created_by: string;
}

export interface OrderMetalAllocation extends BaseEntity {
  allocation_id: string;
  order_id: string;
  pool_id: string;
  allocated_weight: number;
  actual_used_weight?: number;
  expected_return_weight?: number;
  actual_return_weight?: number;
  loss_weight?: number;
  uom_id: string;
  status: 'ALLOCATED' | 'IN_PROCESS' | 'COMPLETED' | 'RETURNED';
  allocation_date: string;
  notes?: string;

  metal_pool?: {
    metal_type?: {
      name?: string; // For metal_type_name
    };
    karat?: {
      purity?: number; // For purity
    };
  };
  uom?: {
    code?: string; // For uom_code
  };
}

export interface OrderDiamond extends BaseEntity {
  diamond_id: string;
  order_id: string;
  receipt_id: string;
  cut_id: string;
  shape_id: string;
  quantity: number;
  weight?: number;
  size_mm?: number;
  uom_id: string;
  status: 'RECEIVED' | 'IN_PROCESS' | 'USED' | 'RETURNED';
  notes?: string;
}

export interface OrderColorStone extends BaseEntity {
  stone_id: string;
  order_id: string;
  receipt_id: string;
  stone_type_id: string;
  shape_id: string;
  quantity: number;
  weight?: number;
  size_mm?: number;
  uom_id: string;
  status: 'RECEIVED' | 'IN_PROCESS' | 'USED' | 'RETURNED';
  notes?: string;
}

export interface OrderPolki extends BaseEntity {
  polki_id: string;
  order_id: string;
  receipt_id: string;
  size_id: string;
  quantity: number;
  weight?: number;
  uom_id: string;
  status: 'RECEIVED' | 'IN_PROCESS' | 'USED' | 'RETURNED';
  notes?: string;
}

export interface ProcessMetalConsumption extends BaseEntity {
  consumption_id: string;
  process_id: string;
  order_id: string;
  pool_id: string;
  expected_weight: number;
  actual_weight: number;
  loss_weight: number;
  uom_id: string;
  consumed_at: string;
  notes?: string;
}

export interface ProcessDiamondConsumption extends BaseEntity {
  consumption_id: string;
  process_id: string;
  order_id: string;
  diamond_id: string;
  expected_quantity: number;
  actual_quantity: number;
  loss_quantity: number;
  consumed_at: string;
  notes?: string;
}

export interface ProcessStoneConsumption extends BaseEntity {
  consumption_id: string;
  process_id: string;
  order_id: string;
  stone_id: string;
  expected_quantity: number;
  actual_quantity: number;
  loss_quantity: number;
  consumed_at: string;
  notes?: string;
}

export interface ProcessPolkiConsumption extends BaseEntity {
  consumption_id: string;
  process_id: string;
  order_id: string;
  polki_id: string;
  expected_quantity: number;
  actual_quantity: number;
  loss_quantity: number;
  consumed_at: string;
  notes?: string;
}

export interface FindingsInventory extends BaseEntity {
  finding_id: string;
  name: string;
  description?: string;
  quantity_on_hand: number;
  uom_id: string;
  is_active: boolean;
}
